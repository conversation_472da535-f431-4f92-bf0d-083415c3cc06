#\u901A\u7528\u4FE1\u606F
system.common.requestSuccess=Request Success
system.common.frequentOperations=The operation is too frequent. Please try again later!
#\u9519\u8BEF\u6D88\u606F
not.null=* Required fill in
verifyCode.not.blank=Verify code cannot be blank
user.jcaptcha.error=Captcha error
user.jcaptcha.expire=Captcha invalid
user.not.exists=Sorry, your account: {0} does not exist
user.password.not.match=User does not exist/Password error
user.password.retry.limit.count=Password input error {0} times
user.password.retry.limit.exceed=Password input error {0} times, account locked for {1} minutes
user.password.delete=Sorry, your account\uFF1A{0} has been deleted
user.blocked=Sorry, your account: {0} has been disabled. Please contact the administrator
role.blocked=Role disabled\uFF0Cplease contact administrators
user.logout.success=Exit successful
length.not.valid=The length must be between {min} and {max} characters
user.username.not.blank=Username cannot be blank
user.username.not.valid=* 2 to 20 chinese characters, letters, numbers or underscores, and must start with a non number
user.username.length.valid=Account length must be between {min} and {max} characters
user.password.not.blank=Password cannot be empty
user.password.length.valid=Password length must be between {min} and {max} characters
user.password.not.valid=* 5-50 characters
user.password.tips.head=Password must meet:
user.password.length.check=Length is between {0} and {1} characters
user.password.digit.check=Contains digits
user.password.case.check=Contains letters
user.password.lowerCase.check=Contains lowercase letters
user.password.upperCase.check=Contains uppercase letters
user.password.specialChar.check=Contains special characters
user.email.not.valid=Mailbox format error
user.email.not.blank=Mailbox cannot be blank
user.email.exists=Mailbox already exists
user.phonenumber.not.blank=Phone number cannot be blank
user.phonenumber.exists=Phone number already exists
user.mobile.phone.number.not.valid=Phone number format error
user.identityType.not.blank=Identity type cannot be blank
user.token.not.blank=Loginn token cannot be blank
user.login.success=Login successful
user.register.success=Register successful
user.register.save.error=Failed to save user {0}, The registered account already exists
user.register.error=Register failed, please contact system administrator
user.register.signType.not.blank=Sign type cannot be blank
user.notfound=Please login again
user.token.expire=The token has expired. Please login again
user.forcelogout=The administrator is forced to exit\uFF0Cplease login again
user.unknown.error=Unknown error, please login again
user.modify.password.old.error=Original password error
user.modify.password.old.new.same=The new password cannot be the same as the original password
user.modify.password.error=Change password failed, please contact system administrator
##\u6587\u4EF6\u4E0A\u4F20\u6D88\u606F
upload.exceed.maxSize=The uploaded file size exceeds the limit file size\uFF01<br/>the maximum allowed file size is\uFF1A{0}MB\uFF01
upload.filename.exceed.length=The maximum length of uploaded file name is {0} characters
upload.file.format.incorrect=Incorrect file format. Please upload the file in {0} format.
upload.image.error=Upload image failed, please contact system administrator
##\u6743\u9650
no.permission=You do not have permission to the data\uFF0Cplease contact your administrator to add permissions [{0}]
no.create.permission=You do not have permission to create data\uFF0Cplease contact your administrator to add permissions [{0}]
no.update.permission=You do not have permission to modify data\uFF0Cplease contact your administrator to add permissions [{0}]
no.delete.permission=You do not have permission to delete data\uFF0Cplease contact your administrator to add permissions [{0}]
no.export.permission=You do not have permission to export data\uFF0Cplease contact your administrator to add permissions [{0}]
no.view.permission=You do not have permission to view data\uFF0Cplease contact your administrator to add permissions [{0}]
no.handle.permission=You do not have permission to operate this function
repeat.submit.message=Repeat submit is not allowed, please try again later
rate.limiter.message=Visit too frequently, please try again later
sms.code.not.blank=Sms code cannot be blank
sms.code.retry.limit.count=Sms code input error {0} times
sms.code.retry.limit.exceed=Sms code input error {0} times, account locked for {1} minutes
email.code.not.blank=Email code cannot be blank
email.code.retry.limit.count=Email code input error {0} times
email.code.retry.limit.exceed=Email code input error {0} times, account locked for {1} minutes
xcx.code.not.blank=Mini program code cannot be blank
##\u79DF\u6237
tenant.administrator.not.found=Sorry, your store user does not exist. Please contact the administrator
tenant.number.not.blank=Store number cannot be blank
tenant.number.error=Store number error
tenant.number.create.error=Store create error
tenant.not.exists=Sorry, your store does not exist. Please contact the administrator
tenant.blocked=Sorry, your store is disabled. Please contact the administrator
tenant.expired=Sorry, your store has expired. Please contact the administrator.
tenant.package.not.found=Store package not found
##\u6570\u636E\u5E93\u76F8\u5173
database.unknownError=Unknown error in the database, please contact the administrator
database.DataTooLong=The filling content is too long. Please check the fields with too long content.
database.totalAmountTooLarge=Total amount too large

##\u901A\u77E5\u516C\u544A
notice.number.create.error=Notification failed creation

##zsmall\u4E1A\u52A1\u76F8\u5173

#\u63A5\u53E3\u6821\u9A8C
zsmall.validated.apiRequired=API required parameter is empty!
zsmall.validated.notNull.walletState=Wallet state cannot be empty
zsmall.validated.notNull.amount=Amount cannot be empty
zsmall.validated.notBlank.note=Remark cannot be empty

zsmall.privacyPolicy.getPrivacyPolicyError=Exception to obtaining privacy policy text
zsmall.privacyPolicy.requestTypeIllegal=Type invalid
zsmall.privacyPolicy.privacyPolicyNotExist=Privacy policy does not exist

#\u7CFB\u7EDF\u76F8\u5173
zsmall.systemCommon.requestSuccess=Request success
zsmall.systemCommon.uploadSuccess=Upload success
zsmall.systemCommon.orderSuccess=Order success!
zsmall.systemCommon.paymentSuccess=Payment success!
zsmall.systemCommon.hasPriceChange=Your price change has been submitted for review, the latest price will be effective within three business days, please check the product review page.
zsmall.systemCommon.requiredCannotEmpty=[API]Required item cannot be empty!
zsmall.systemCommon.illegalParameter=[API]Illegal parameter\uFF01
zsmall.systemCommon.uploadFileNotResponding=Upload are not responding, please try again.
zsmall.systemCommon.downloadFileError=An exception occurred when download file.
zsmall.systemCommon.systemBusy=The system is busy. Please try again later.
zsmall.systemCommon.logQueryError=An unknown error occurred when query log.
zsmall.systemCommon.missingValueInTheInterface=Missing required value in the APIs.
zsmall.systemCommon.functionIsNotOpen=This function is not open.
zsmall.systemCommon.businessCodeGenerateError=Business code generation failed.
zsmall.systemCommon.theRelevantDataHasChanged=The relevant data has changed. Please refresh and try again.

#\u5168\u5C40\u76F8\u5173
zsmall.global.notLogin=The user account does not exist or the Token is invalid. Please log in again.
zsmall.global.currentUserNotMd=The current account not staff account and cannot be operated.
zsmall.global.currentUserNotBulk=The logged in account is not registered as a dropshipper. Please log in with a dropshipper account to continue.
zsmall.global.currentUserNotSup=The current account not supplier account and cannot be operated.
zsmall.global.currentUserCannotUse=The current account cannot use this function.
zsmall.global.currentUserNotMdOrNotSup=The current account not staff or not supplier account and cannot be operated.
zsmall.global.userAccountNotExist=The user account does not exist
zsmall.global.updateUserMessageError=Unknown error occurred when update the user message.
zsmall.global.userInvitationCodeExist=Invitation code already exists
zsmall.global.illegalCharactersExist=Illegal characters exist.
zsmall.global.registerInformationInputTimeout=register information input timeout, please register again.
zsmall.global.resetPasswordEmailTimeout=Input password has timed out, please send to mail again.
zsmall.global.updateEmailTimeout=Input email message has timed out, please send to mail again.
zsmall.global.emailLinkTimeout=Mail is invalid, please send to mail again.
zsmall.global.currentUserPhoneNotExist=The user has not bound a phone number
zsmall.global.emailVerifyCodeTimeout=The verification code is invalid, please send to mail again.
zsmall.global.emailVerifyCodeCompareError=Unknown error occurred when compare verify code.
zsmall.global.verifyCodeIsNotMatch=The verification code does not match. Please enter again.
zsmall.global.mailboxAlreadyExists=The mailbox already exists. Please enter again.
zsmall.global.phoneAlreadyExists=The phone number already exists. Please enter again.
zsmall.global.downloadExcelError=An exception occurred when download excel.
zsmall.global.uploadExcelError=An exception occurred when upload excel.
zsmall.global.userNotExist=The user does not exist
zsmall.global.exportFileError=An unknown error occurred when export file.
zsmall.global.accountIsFrozen=The account is frozen, please contact the administrator.
zsmall.global.newOldPasswordRepeated=New and old passwords cannot be repeated.
zsmall.global.menuPermissionNotExist=The user has not menu permissions
zsmall.global.deleteRedisError=An unknown error occurred when delete redis.
zsmall.global.systemErrorE10028=System unknown error, please contact the administrator(Z028).
zsmall.global.systemErrorE10029=System unknown error, please contact the administrator(Z029).
zsmall.global.passwordError=The password is incorrect. Please try again.
zsmall.global.userNotReviewError=The account is under review.
zsmall.global.comfirmPasswordIsNotMatch=The two passwords are inconsistent
zsmall.global.inventoryChangeError=Unknown error occurred when inventory change.


#\u7528\u6237\u76F8\u5173
zsmall.userOperation.queryPassedGuideError=An exception occurred when query beginner guide.
zsmall.userOperation.passGuideError=An exception occurred when pass beginner guide.
zsmall.userOperation.switchAutoDeductionError=An exception occurred when switch the auto deduction.
zsmall.userOperation.getAutoDeductionError=Unknown error occurred while processing auto payment. Please try again later.
zsmall.userOperation.phoneNumberNotMeetSpecification=The length of the phone number does not meet the specification.
zsmall.userOperation.firstResetPasswordError=An exception occurred when first reset password.
zsmall.userOperation.notNeedResetPassword=Your account does not need to reset password.
zsmall.userOperation.systemMaintenance=System Maintenance.


#\u7528\u6237\u9996\u9875
zsmall.userHomePage.getHomePageProductError=An exception occurred when get home page product.

#\u7528\u6237\u53CD\u9988
zsmall.userFeedBack.userFeedbackSubmitError=An exception occurred when user submitted feedback.
zsmall.userFeedBack.findUserFeedbackSubmitError=An exception occurred when querying user submitted feedback.
zsmall.userFeedBack.userFeedbackNotExist=User feedback does not exist.
zsmall.userFeedBack.getUserFeedbackDetailError=Failed to obtain user feedback details.
zsmall.userFeedBack.userFeedbackIsAccept=The user's feedback has been accepted, please do not accept it again.
zsmall.userFeedBack.changeAcceptStatusError=Abnormal change of acceptance status.
zsmall.userFeedBack.userIsNotMdPermissionToChangeStatus=Non employees cannot change the acceptance status without permission.


#\u5546\u54C1\u5206\u7C7B\u76F8\u5173
zsmall.productCategory.resetProductCategoryError=An exception occurred when reset product category.
zsmall.productCategory.productCategoryExistProduct=There are product in the category and cannot be deleted. (Product Code: {0})
zsmall.productCategory.querySiteMapCategoryError=An exception occurred when query site map category.
zsmall.productCategory.productCategoryNotFound=The category does not exist.


#\u5546\u54C1\u94FA\u8D27\u76F8\u5173
zsmall.productDropShipping.productSkuMarkUpNotSet=Product not set Mark UP.
zsmall.productDropShipping.productSkuMarkUpNotZero=Mark UP cannot be 0!
zsmall.productDropShipping.productSkuMappingQueryError=An exception occurred when query sku mapping.
zsmall.productDropShipping.productSkuMappingSaveError=An exception occurred when save sku mapping.
zsmall.productDropShipping.productSkuNotExist=Sku not exist or deleted
zsmall.productDropShipping.productSkuDeleteError=An exception occurred when delete sku.
zsmall.productDropShipping.mappingSkuRequired=Mapping sku required.
zsmall.productDropShipping.mappingSkuRepeat=Duplicate mapping sku ({0}) are not allowed in the same store.
zsmall.productDropShipping.syncProductSkuError=An exception occurred when sync sku to sale channel.
zsmall.productDropShipping.notSetMapping=Mapping is not set and cannot be synchronized to the channel.
zsmall.productDropShipping.querySkuWarehouseConfigsError=An unknown error occurred when query sku warehouse configs.
zsmall.productDropShipping.getDropshippingListError=An unknown error occurred when query dropshipping product.
zsmall.productDropShipping.exportDropshippingListError=An unknown error occurred when export dropshipping product.
zsmall.productDropShipping.mappingSkuRepeatShopify=Previously synced products will not be synced again to your Shopify store.
zsmall.productDropShipping.outOfStoreSyncFailed=Product inventory is insufficient and cannot be synchronized to Shopify.
zsmall.productDropShipping.selectAtLeastOneChannel=Please select at least one channel store.
zsmall.productDropShipping.selectAtLeastOneSku=Please select at least one SKU.
#\u5546\u54C1\u76F8\u5173
zsmall.product.productSkuCodeExisted=SKU[{0}] repeat!
zsmall.product.productUpcCodeExisted=UPC[{0}] repeat!
zsmall.product.productSkuNotMatching=Product sku does not match.
zsmall.product.setSaleStatusError=An exception occurred when set sale status.
zsmall.product.skuAttributesRequire=Product specifications related information must be required, please check carefully.
zsmall.product.skuInformationRequire=SKU information must be required, please check carefully.
zsmall.product.queryProductSkuPageError=An exception occurred when query product sku page.
zsmall.product.skuRequiresOneImage=Sku requires at least one image.
zsmall.product.supAddProductAttributeError=An unknown error occurred when add custom specification.
zsmall.product.productCategoryNotFoundNotDefinedSpecifications=Category not found and cannot add custom specification.
zsmall.product.specValuesRepeat=SKU specification values repeat.
zsmall.product.uploadProductQuantityUpdateError=An unknown error occurred when upload product quantity update excel.
zsmall.product.uploadProductPriceUpdateError=An unknown error occurred when upload product price update excel.
zsmall.product.queryExportFieldMdError=An unknown error occurred when query export field.
zsmall.product.exportProductFileMdError=An unknown error occurred when export product message.
zsmall.product.notChooseExportFieldMdError=Please choose export field.
zsmall.product.notSupport3rdBilling=This product is located in the warehouse does not support logistics 3rdbilling account.
zsmall.product.logisticsTemplateNotSelect=No logistics template.
zsmall.product.warehouseTypeIsNotRecognized=The type of warehouse is not recognized, please select the first warehouse again.
zsmall.product.queryItemNoError=An unknown error occurred when query Item No.
zsmall.product.notConfiguredLogisticsTemplate=This product is located in the warehouse does not configured with a logistics template.
zsmall.product.notHaveEnoughStock=This product is located in the warehouse does not have enough stock.
zsmall.product.pickUpTypeUnknown=Pick Up type unknown.
zsmall.product.skuPickUpDropshippingPriceNotZero=Sku[{0}] Unit price & Dropshipping price cannot be 0
zsmall.product.skuPickUpPriceNotZero=Sku[{0}] Unit price cannot be 0
zsmall.product.skuDropshippingPriceNotZero=Sku[{0}] Dropshipping price cannot be 0
zsmall.product.newProductVerifyPending=The new product is being reviewed, and the information cannot be modified
zsmall.product.reviewProductError=An unknown error occurred when review product.
zsmall.product.rejectionReasonCannotBeEmpty=The reason for review rejection cannot be blank.
zsmall.product.queryProductReviewPageError=An unknown error occurred when query product review list.
zsmall.product.productNotExist=The product not exist.
zsmall.product.queryProductError=An unknown error occurred when query product
zsmall.product.outOfStock=The stock does not enough.
zsmall.product.deductStockError=An unknown error occurred when deduct stock.
zsmall.product.specifyDateNotEqualNowOrLess=The specified effective date cannot be less than or equal to today.
zsmall.product.productNotExistOrNotOnShelf=Product does not exist.
zsmall.product.warehouseNotSelected=Warehouse not selected.
zsmall.product.productDataError=Product data error, please try again.
zsmall.product.productQueryListError=An unknown error occurred when query product list.
zsmall.product.productCategoryUpdateError=An unknown error occurred when update product category.
zsmall.product.productOffShelfError=An unknown error occurred when off shelf product.
zsmall.product.skuUnitPriceNotZero=Sku[{0}] Unit price cannot be 0
zsmall.product.skuOperationFeeNotZero=Sku[{0}] Operation fee cannot be 0
zsmall.product.skuFinalDeliveryFeeNotZero=Sku[{0}] Final delivery fee cannot be 0
zsmall.product.skuUnitPriceOperationFeeFinalDeliveryFeeNotZero=Sku[{0}] Unit price & Operation fee & Final delivery fee cannot be 0
zsmall.product.newProductPriceVerifyPending=The product is being reviewed, and the price cannot be modified.
zsmall.product.productAccepted=The product has passed the review, and does not need to be submitted again for review.
zsmall.product.setProductSkuMarkUpError=An exception occurred when set sku mark up.
zsmall.product.notReviewCannotOnShelf=Products that have not passed the review cannot be on shelf.
zsmall.product.productGlobalAttributeNameRepeat=Attribute name repeat!
zsmall.product.productSkuNotExists=Product SkuId.[{0}] not exists!
zsmall.product.productSkuOutOfStock=Product SkuId.[{0}] out of stock!
zsmall.product.productSkuAdjustStockError=Product ItemNo.[{0}] unknown error occurred when adjust stock.
zsmall.product.productMappingException=Product not mapped orderNo.[{0}]!
zsmall.product.delisted=The product has been taken down and payment cannot be made
zsmall.finalDeliveryFee.exception = The end delivery fee is abnormal!
zsmall.finalDeliveryFee.measurementAnomaly = Abnormal calculation caused by insufficient inventory!
zsmall.siteInformation.aScene = The site cannot be empty!
zsmall.verifyCode.sendFail=Verification code failed to send, please contact the administrator!
zsmall.excel.onlyExcelIsSupported =The format of the Other attachment is incorrect. The attachment only supports excel files!
zsmall.order.ltlOrderMustNeedBol = Orders with ltl as the carrier must be bol!

zsmall.order.openOrderReviewConsentSuccess  = The supplier order was approved and the business operation was successful!

zsmall.order.openOrderReviewConsentFailed   = The business operation failed when the supplier order was approved!
zsmall.order.openOrderReviewRefuseSuccess   = The business operation of the supplier order approval rejection was successful!
zsmall.order.openOrderReviewRefuseFailed   = The business operation of the supplier order approval rejection failed!

zsmall.order.abnormalSkuInventoryIsInsufficient = The inventory is insufficient. Please contact the administrator!
zsmall.order.abnormalCreatedForTheShippingOrder =There is an exception in creating the shipping order. Please contact the administrator!
#\u5B9A\u4EF7\u76F8\u5173
zsmall.productPrice.productSkuPriceRuleSaveError=An exception occurred when save price rule and applicable products.
zsmall.productPrice.productSkuApplicableProductQueryError=An exception occurred when query applicable product.
zsmall.productPrice.productSkuApplicableProductDeleteError=An exception occurred when delete applicable product.
zsmall.productPrice.queryProductSkuPriceListError=An exception occurred when get the list of product price information
zsmall.productPrice.productSkuPriceSaveError=An exception occurred when save price information
zsmall.productPrice.queryProductSkuPriceRuleListError=An exception occurred when querying the list of pricing formulas
zsmall.productPrice.queryProductSkuPriceRuleDetailsError=An exception occurred when querying the pricing formula details
zsmall.productPrice.applicableValueError=The applicable data of product price formula has intersection.
zsmall.productPrice.checkApplicableValueError=An exception occurred when verify the applicable data of product price formula.
zsmall.productPrice.applicableProductCannotBeDeleted=Current applicable products cannot be deleted.
zsmall.productPrice.applicableCalculationError=Cannot multiply by 0 and divide by 0.
zsmall.productPrice.applicableBaseRuleCannotEdit=Basic rule cannot be update.

#UPC\u76F8\u5173
zsmall.productUpc.geyUsageUpcError=An exception occurred when query valid upc list.
zsmall.productUpc.upcShortage=Upc is insufficient.
zsmall.productUpc.usageUpcRecordUpdateError=An exception occurred when update the upc usage record.
zsmall.productUpc.usageUpcStoreUsed=UPC has been used by other stores.
zsmall.productUpc.usageUpcSkuUsed=UPC has been used by other SKUs in the current store
zsmall.productUpc.usageUpcNotFound=UPC is missing or has been used

#\u8BA2\u5355\u76F8\u5173
zsmall.orders.payOrderSuccess=The payment request has been submitted to the system for processing. Please check the order status for the payment results.
zsmall.orders.orderItemNotExist=Sub-order does not exist.
zsmall.orders.logisticsTrackingNotExist=The logistics information does not exist, and the performance status cannot be changed.
zsmall.orders.shippingLabelDownloadError=An exception occurred when download shipping label.
zsmall.orders.fileCantBeNotFound=File does not exist in the server.
zsmall.orders.uploadFileIsEmpty=Upload file is empty.
zsmall.orders.excelColumnCountNotMatch=The title row of the uploaded template file is incorrect. Please download the template again and do not modify the title row of the template.
zsmall.orders.excelNotExistValidRow=Parameters in the sheet cannot be empty.
zsmall.orders.orderNotExistOrReview=The order does not exist or the import record has been reviewed.
zsmall.orders.shippingLabelUploadError=An exception occurred when upload shipping label.
zsmall.orders.orderUnpaidCantFulfill=The order status has not been [Paid] and cannot confirm dispatched.
zsmall.orders.getPayFailedOrderMessageError=Unknown error occurred when getting failed payment information. Please try again later.
zsmall.orders.orderRefundingCantFulfill=The order cannot be fulfilled during the refund.
zsmall.orders.orderCancelError=An exception occurred when cancel order.
zsmall.orders.payOrderError=An exception occurred when pay order.
zsmall.orders.automaticallyDeductionTurnedOn=You have turned on auto payment. Turn it off if you wish to pay manually.
zsmall.orders.productImportFileSuffixNotMatch=Product import file suffix is not xls or xlsx
zsmall.orders.productImportFileTitleNotMatch=The title of the product import file does not meet the standard. Please download the template again.
zsmall.orders.productImportFileExistImporting=There is an Excel that is being imported, please wait for the import end.
zsmall.orders.productImportError=An unknown error occurred when uploading product import excel.
zsmall.orders.queryProductImportRecordError=An unknown error occurred when query proudct import record.
zsmall.orders.connectOtherLogisticsError=An unknown error occurred when connecting other's logistics.
zsmall.orders.orderNotExist=Order not exist.
zsmall.orders.orderHasNotBeenPaid=The order has not been paid, unable to perform this operation.
zsmall.orders.productImportFileNotRow=No product data in file.
zsmall.orders.fulfillmentStatusCannotGoBack=The fulfillment status cannot be returned to the previous status.
zsmall.orders.shippingLabelOrderTooMuch=There are multiple duplicate Channel Order No. that cannot match the shipping label. Please upload the shipping label with a single order operation.
zsmall.orders.notNeedUploadLabel=The order has been filled in the [3rd Billing], and there is no need to upload the shipping label.
zsmall.orders.unsupportedCountries=Unsupported countries.
zsmall.orders.uploadShippingLabelMessage=The following file does not match the [Store Order ID]: </br>{0}
zsmall.orders.changeChannelOrderIdError=An unknown error occurred when changing the channel order id
zsmall.orders.changeStoreLinkError=An unknown error occurred when changing the store link
zsmall.orders.changeOrderDetailsError=An unknown error occurred when changing order details
zsmall.orders.productOnlySupportDropshipping=Products only support dropshipping
zsmall.orders.productOnlySupportPickUp=Products only support pick up
zsmall.orders.placeShoppingCartOrderError=An unknown error occurred while placing a shopping cart order
zsmall.orders.orderConfirmReceiptError=An unknown error occurred when confirming receipt of order.
zsmall.orders.orderCannotUpdateNotBelongToMeError=There are no orders waiting for receipt.
zsmall.orders.orderAttachmentUploadError=An exception occurred when upload order attachment.
zsmall.orders.queryOrderListError=An unknown error occurred while querying the order list
zsmall.orders.queryOrderDetailError=An unknown error occurred while querying order details
zsmall.orders.currentOrderNotSupported=This function is not supported on the current order.
zsmall.orders.onlySupportsDropShipping=The product [{0}] only supports 'DropShipping'.
zsmall.orders.onlySupportsPickUp=The product [{0}] only supports 'PickUp'.
zsmall.orders.notSupportedShipTo=Product not supports ship to {0}.
zsmall.orders.orderPayUnknownError=An unknown error occurred during order payment. Please try again.
zsmall.orders.orderStockAdjustUnknownError=An Unknown error occurred during order stock adjustment. Please try again.
zsmall.orders.orderAmountNotMatch=The order amount does not match and cannot be paid.
zsmall.orders.orderStateHasChanged=The Order state has changed. Please pay again.
zsmall.orders.orderStateException=The order state is abnormal. Please pay again. If it occurs multiple times, please contact the platform staff.
zsmall.orders.orderShippingLabelNotExists=Order shipping label not exists
zsmall.orders.inconsistentShippingLabelAndTrackingNo=The quantity of shipping label is inconsistent with that of tracking No.
zsmall.orders.thirdWarehouseCreateShippingOrderError=An unknown error occurred during third-party warehouse create shipping order
zsmall.orders.wayfairRegisterShippingFailed=Wayfair register shipping failed. Wayfair API did not return relevant data, please try again later.
zsmall.orders.wayfairRegisterShippingEncounteredError=Wayfair register shipping encountered an unknown error, reason: {0}
zsmall.orders.orderPayError.message=Order payment failed, failure message: [{0}]
zamall.orders.orderNoTrackingInfo=The order does not have tracking information. Please fill in the tracking information and make the payment again
zsmall.orders.skuRegionPriceNotMaintained =SKU {0} price not maintained in this region
zamall.orders.orderCannotMatchWarehouse=Order cannot match warehouse
zsmall.orders.orderActivityException.stockPullLockException=Abnormalities caused by ERP inventory pulling
zsmall.orders.orderActivityException.erpLockException=The inventory interface of the ERP system is experiencing an abnormality
zsmall.orders.orderActivityException.erpReleaseException=Abnormality in the inventory interface for releasing locked goods in ERP
zsmall.orders.orderException.releaseException=Failed to release inventory for active orders

#\u8BA2\u5355\u7269\u6D41\u76F8\u5173
zsmall.orderLogistics.confirmDispatchedError=An unknown error occurred when confirm dispatched.
zsmall.orderLogistics.orderAlreadyDispatched=Currently unable to operate shipment.
zsmall.orderLogistics.fillLeastOneTrackingNo=Please fill in at least one Tracking No.
zsmall.orderLogistics.discernLogisticsError=An unknown error occurred when discern logistics info.
zsmall.orderLogistics.track17DiscernLogisticsError=Discern logistics info error, reason: {0}.
zsmall.orderLogistics.trackingNoRequired=Tracking No. required!
zsmall.orderLogistics.thirdLogisticsError=Unknown error occurred in Third-party logistics service, Tracking No.[{0}], reason: {0}

#\u8BA2\u5355\u7269\u6D41\u8DDF\u8E2A\u76F8\u5173
zsmall.orderLogisticsTracking.queryTrackingPageError=An unknown error occurred when query logistics tracking.
zsmall.orderLogisticsTracking.exportTrackingError=An unknown error occurred when export logistics tracking data.

#\u8BA2\u5355\u9000\u6B3E\u76F8\u5173\u5F02\u5E38
zsmall.orderRefund.noCanRefundOrderItem=The order has been refunded or does not meet the refund standard.
zsmall.orderRefund.getRefundReasonOptionsError=Unknown error occurred when getting refund reasons. Please try again later.
zsmall.orderRefund.refundAllWhenUndispatched=Only all quantity refund can be made if undispatched.
zsmall.orderRefund.hasRefundingOrder=There is a refund order in progress. Please wait until it is completed and reapply.
zsmall.orderRefund.orderExceededAfterSalesLimitations=Your order has exceeded the after-sales statute of limitations, if the after-sales demand please contact us.


#\u8BA2\u5355\u65B0\u7248\u552E\u540E\u76F8\u5173
zsmall.orderAfterSales.queryRefundReasonListError=An unknown error occurred when query refund reason list.
zsmall.orderAfterSales.submitRefundRequestError=An unknown error occurred when submit refund request.
zsmall.orderAfterSales.refundRuleNotExist=Refund rule do not exist, please refresh page and re-apply.
zsmall.orderAfterSales.uploadRefundRuleError=An unknown error occurred when upload refund rule.
zsmall.orderAfterSales.cancelRefundRequestError=An unknown error occurred when cancel refund request.
zsmall.orderAfterSales.refundRequestNotExist=Refund request record not exist.
zsmall.orderAfterSales.cannotCancelTheRefundApplication=The current status cannot cancel the refund application.
zsmall.orderAfterSales.queryRefundDetailError=An unknown error occurred when query refund detail.
zsmall.orderAfterSales.refundHandleError=Abnormal refund application processing.
zsmall.orderAfterSales.currentRefundCannotBeProcessed=The operation cannot be performed in the current refund application status.
zsmall.orderAfterSales.confirmReceiptOfReturn=An unknown error occurred when confirm receipt of return.
zsmall.orderAfterSales.returnLogisticsInformationNotExist=Return logistics information does not exist.
zsmall.orderAfterSales.platformInterventionError=An unknown error occurred when application platform intervention.
zsmall.orderAfterSales.handlePlatformInterventionError=An unknown error occurred when handle platform intervention.
zsmall.orderAfterSales.orderRefunded=The order has been fully refunded, and the refund application cannot be submitted again.
zsmall.orderAfterSales.pleaseProvidePictures=Please provide pictures.
zsmall.orderAfterSales.amountCannotBeZero=Amount cannot be 0!
zsmall.orderAfterSales.requestedAmountExceedsRefundable=The requested amount exceeds the refundable amount.

#\u4ED3\u5E93\u7BA1\u7406\u76F8\u5173

zsmall.warehouseManagement.saveStoreWarehouseError=An exception occurred when save warehouse.
zsmall.warehouseManagement.theSameWarehouseIdExists=The same warehouse code exists.
zsmall.warehouseManagement.getStoreWarehouseListByTypeError=An exception occurred when get store warehouse list.
zsmall.warehouseManagement.getWarehouseKeyListError=An exception occurred when set get warehouse key.
zsmall.warehouseManagement.setWarehouseKeyError=An exception occurred when set warehouse key.
zsmall.warehouseManagement.theSameKeyIdExists=The same warehouse key id exists.
zsmall.warehouseManagement.theKeyMustBeNumber=The key must be number.
zsmall.warehouseManagement.associatedProductCannotDelete=The warehouse is associated with product and cannot be deleted.
zsmall.warehouseManagement.associatedLogisticsCannotDelete=The warehouse is associated with logistics template and cannot be deleted.
zsmall.warehouseManagement.queryProductSkuStockListError=An exception occurred when query product stock list.
zsmall.warehouseManagement.editStockQuantityError=An exception occurred when editing the inventory quantity.
zsmall.warehouseManagement.bizarkWarehouseCannotEditStock=Third-party warehouse cannot edit stock.
zsmall.warehouseManagement.switchStockSaleStatusError=An exception occurred when switch stock sale status.
zsmall.warehouseManagement.stockStatusUnknown=Stock status unknown and cannot operate.
zsmall.warehouseManagement.queryValidWarehouseListError=An exception occurred when query valid warehouse list.
zsmall.warehouseManagement.skuExistNowWarehouse=The SKU is already in the warehouse.
zsmall.warehouseManagement.warehouseNotExist=Invalid or deleted warehouse.
zsmall.warehouseManagement.queryFulfillWarehouseListError=An exception occurred when query fulfill warehouse list.
zsmall.warehouseManagement.settingFulfillWarehouseError=An exception occurred when setting fulfill warehouse.
zsmall.warehouseManagement.queryProductSkuStockError=An exception occurred when query product stock.
zsmall.warehouseManagement.queryTransportMethodError=An exception occurred when query transport method.
zsmall.warehouseManagement.queryProductSkuListError=An exception occurred when query product sku list.

#\u7B2C\u4E09\u65B9\u5E73\u53F0\u76F8\u5173
zsmall.thirdPartyPlatforms.shopifyRedirectError=Shopify app authorization redirect exception.
zsmall.thirdPartyPlatforms.shopifyAccessTokenNotExist=Shopify Access Token does not exist.
zsmall.thirdPartyPlatforms.getChannelInfoError=An exception occurred when get channel info.
zsmall.thirdPartyPlatforms.syncProductUnknownError=An unknown error occurred when sync product to channel.
zsmall.thirdPartyPlatforms.getEnableChannelError=An unknown error occurred when get enable channel.
zsmall.thirdPartyPlatforms.queryImportRecordError=An unknown error occurred when query import record.
zsmall.thirdPartyPlatforms.downloadSheetError=Unknown error occurred when downloading import sheet. Please try again later.
zsmall.thirdPartyPlatforms.reviewImportRecordError=An unknown error occurred when review import record.
zsmall.thirdPartyPlatforms.importRecordNotFound=Import history cannot be found.
zsmall.thirdPartyPlatforms.orderLackShippingLabel=The order lacks shipping label and cannot be review.
zsmall.thirdPartyPlatforms.queryImportRecordDetailError=Unknown error occurred when getting import history. Please try again later.
zsmall.thirdPartyPlatforms.queryImportRecordOrderListError=Unknown error occurred when getting imported order list. Please try again later.
zsmall.thirdPartyPlatforms.queryEnableChannelGroupError=An unknown error occurred when query channel group.
zsmall.thirdPartyPlatforms.modifyChannelInfoError=An unknown error occurred when modify channel info.
zsmall.thirdPartyPlatforms.salesChannelNotExist=The sales channel has been deactivated or deleted, and related operations cannot be performed.
zsmall.thirdPartyPlatforms.salesChannelCannotSync=Sales channel not enabled or deleted, unable to synchronize product.
zsmall.thirdPartyPlatforms.syncStatusNotCorrect=The sync status is incorrect, current operation cannot be performed.
zsmall.thirdPartyPlatforms.requestSyncStatusUnknown=Request sync status unknown.
zsmall.thirdPartyPlatforms.sameChannelStoreName=There is the same channel store name.
zsmall.thirdPartyPlatforms.sameChannel=There is the same channel store info.
zsmall.thirdPartyPlatforms.getChannelDetailInfoError=An unknown error occurred when get channel detail info.
zsmall.thirdPartyPlatforms.queryAllChannelGroupError=Unknown error occurred when getting sales channel information. Please try again later.
zsmall.thirdPartyPlatforms.wayfairSandboxTestUnknownError=Unknown error occurred when wayfair sandbox testing.
zsmall.thirdPartyPlatforms.wayfairSandboxTestErrorNotOrders=Wayfair sandbox test error(No valid order found).
zsmall.thirdPartyPlatforms.authorizeInfoLost=Loss of authorization information, please re-authorize
zsmall.thirdPartyPlatforms.shopifyHasConnected=Shopify stores have been connected by other accounts.
zsmall.thirdPartyPlatforms.shopifyAuthenticationExpired=Shopify authorization information has expired, please go to the Shopify store and click on Application to authorize again.
zsmall.thirdPartyPlatforms.salesChannelOnlyBulk=Your Shopify mailbox has been registered with a non-distributor account, and only the distributor can connect the sales channel.
zsmall.thirdPartyPlatforms.otherOrderImportError=An unknown error occurred when import others other.
zsmall.thirdPartyPlatforms.tempOrderNotFoundError=Unable to find order, please try again later.
zsmall.thirdPartyPlatforms.tempOrderUpdateError=An unknown error occurred when update order, please try again later.
zsmall.thirdPartyPlatforms.tempOrderStateNotExist=The state does not exist.
zsmall.thirdPartyPlatforms.tempOrderPhoneError=The format of the phone number is wrong.
zsmall.thirdPartyPlatforms.tempOrderCountryNotExist=Unsupported countries.
zsmall.thirdPartyPlatforms.tempOrderRecipientNameBlankError=The recipient name is empty.
zsmall.thirdPartyPlatforms.tempOrderPhoneBlankError=The format of the phone number is wrong.
zsmall.thirdPartyPlatforms.tempOrderAddress1BlankError=The address1 is empty.
zsmall.thirdPartyPlatforms.tempOrderCityBlankError=The city is empty.
zsmall.thirdPartyPlatforms.tempOrderZipcodeBlankError=The zipcode is empty.
zsmall.thirdPartyPlatforms.tempOrderLogisticsAccountBlankError=The logistics account is empty.
zsmall.thirdPartyPlatforms.tempOrderLogisticsAccountZipcodeBlankError=The logistics account zipcode is empty.
zsmall.thirdPartyPlatforms.tempOrderWarehouseNotSupport3rdbillingError=The warehouse does not support third-party logistics.
zsmall.thirdPartyPlatforms.tempOrderTrackingNoBlankError=The tracking NO. is empty.
zsmall.thirdPartyPlatforms.tempOrderStoreOrderIdBlankError=You can not upload shipping labels, Because store order ID is empty
zsmall.thirdPartyPlatforms.tempOrderStoreOrderIdBlankOrNotPickUpError=There is no order to upload the shipping label
zsmall.thirdPartyPlatforms.tempOrderPickUpNotExistShippingLabelError=Pick up order not upload shipping label.
zsmall.thirdPartyPlatforms.tempOrderSomeProductNotExist=Item No.[{0}] has been taken off the shelf or not exist!
zsmall.thirdPartyPlatforms.tempOrderProductNotExistWarehouse=Item No.[{0}] does not exist in the specified warehouse({1})!
zsmall.thirdPartyPlatforms.tempOrderZipcodeNotExist=The zip code is illegal.(format 00000 or 00000-0000)
zsmall.thirdPartyPlatforms.tempOrderStoreOrderIdRepeat=Store order ID can not repeat.
zsmall.thirdPartyPlatforms.salesChannelDeleteDisconnect=Sales channel store deleted or already disconnect
zsmall.thirdPartyPlatforms.salesChannelUpdateSuccess=Sales channel store: {0}, ItemNo.: {0}, Information update success
zsmall.thirdPartyPlatforms.salesChannelUpdateFailed=Sales channel store: {0}, ItemNo.: {0}, Information update failed, reason: {0}
zsmall.thirdPartyPlatforms.productSynchronizing=The product [{0}] are being processed, please try again later.
zsmall.thirdPartyPlatforms.tempOrderFileDeleteError=File deletion error, please try again
zsmall.thirdPartyPlatforms.tempOrderCarrierBlankError=Third-party logistics must be select carrier.
zsmall.thirdPartyPlatforms.pushFulfillmentToSalesChannelError=An unknown error occurred when push fulfillment info to sales channel.
zsmall.thirdPartyPlatforms.shopifyFulfillmentOrderNotExists=Shopify fulfillment order does not exist, please confirm if it has been manually fulfilled.


#\u5546\u5E97
zsmall.store.getAccessTokenFailed=Get access token failed.
zsmall.store.channelStoreNotExistOrDeleted=Channel store not exist or deleted.
zsmall.store.wayfairConnectError=WayFair Connect failed.
zsmall.store.bindingChannelAccountError=Channel binding failed.
zsmall.store.jumpToChannelError=Jump to channel failed.
zsmall.store.querySalesChannelStatusError=Abnormal query of sales channel association status.
zsmall.store.querySalesChannelInfoError=Abnormal query of sales channel.
zsmall.store.disconnectSalesChannelError=Channel unbind failed.
zsmall.store.channelTypeIsEmpty=Channel type cannot be empty!
zsmall.store.channelNameIsEmpty=Channel name cannot be empty!

#\u652F\u4ED8\u65B9\u6CD5\u76F8\u5173
zsmall.payMethod.calculateHandleFeeError=An exception occurred when calculate handle fee.
zsmall.payMethod.unknownPaymentType=Unknown payment type.
zsmall.payMethod.checkBindPaymentMethodError=An unknown error occurred when check bind payment method.
zsmall.payMethod.costLessHandingFee=The deposit amount is less than or equal to the transaction fee, and the deposit cannot be completed.
zsmall.payMethod.rechargeOrDeductError=An unknown error occurred when recharge or deduct balance.
zsmall.payMethod.bulkOrSupNotExist=Bulk or supplier is not exist.
zsmall.payMethod.rechargeOrDeductOperationTypeError=Please select the correct operation type.
zsmall.payMethod.bulkWalletBalanceInsufficient=Insufficient wallet balance.
zsmall.payMethod.clearBalanceError=An unknown error occurred when clear balance.
zsmall.payMethod.userNotSupOrNotBulk=The be operated user is not a bulk or supplier.
zsmall.payMethod.queryBalanceError=An unknown error occurred when query balance.
zsmall.payMethod.exportBalanceError=An unknown error occurred when export balance.
zsmall.payMethod.onlyAllowRechargeToBulkWallet=Only allow recharging for distributor users.
zsmall.payMethod.bulkNotExist=Bulk is not exist.
zsmall.payMethod.walletFreeze=The wallet has been frozen and cannot be operated.
zsmall.payMethod.notSupportedBankCards=Bank cards are not supported for order payment.
zsmall.payMethod.invalidPaymentMethod=Invalid payment method, please select again.
zsmall.payMethod.walletPayError=An unknown error occurred when wallet pay.
zsmall.payMethod.walletRefundError=An unknown error occurred when refunding to wallet.
zsmall.payMethod.notAllowRechargeToSup=The function of recharging for suppliers is not opened temporarily.
zsmall.payMethod.walletUpdateFailure=Wallet update failed, please try again.
zsmall.payMethod.walletNotFound=Wallet not found.

#\u652F\u4ED8\u6269\u5C55\u76F8\u5173
zsmall.payExtend.getRemittanceInfoError=An unknown error occurred when get remittance info.
zsmall.payExtend.submitPaymentReceiptError=An unknown error occurred when submit payment receipt info.
zsmall.payExtend.queryPaymentReceiptError=An unknown error occurred when query payment receipt info.
zsmall.payExtend.reviewPaymentReceiptError=An unknown error occurred when review payment receipt info.
zsmall.payExtend.paymentReceiptSolved=This payment receipt has solved, cannot handle again!
zsmall.payExtend.paymentReceiptNotExist=Payment receipt not exist.
zsmall.payExtend.getWalletDetailsListError=Unknown error occurred when getting wallet details list. Please try again later.
zsmall.payExtend.exportWalletDetailsListError=Unknown error occurred when export wallet details list. Please try again later.
zsmall.payExtend.queryPaymentReceiptDepositError=An unknown error occurred when query payment receipt deposit info.
zsmall.payExtend.queryPaymentReceiptTotalAmountError=An unknown error occurred when query payment receipt total amount.
zsmall.payExtend.queryPaymentReceiptWithdrawalError=An unknown error occurred when query payment receipt withdrawal info.
zsmall.payExtend.storeTransactionIsEmpty=Transaction record does not exist.
zsmall.payExtend.queryRechargeDetailsError=An unknown error occurred when querying recharge details
zsmall.payExtend.exportPaymentReceiptDepositError=An unknown error occurred when export payment receipt deposit info.
zsmall.payMethod.deposit.cost.notLess0=Deposit amount must be greater than 0.

#\u5546\u54C1\u95EE\u7B54\u76F8\u5173
zsmall.productQA.getQuestionsListError=Exception occurred when query information of product question list.
zsmall.productQA.getQuestionsDetailsError=Unknown error occurred when getting question list. Please try again later.
zsmall.productQA.replyQuestionsError=Unknown error occurred when replying question. Please try again later.
zsmall.productQA.createQuestionsError=Unknown error occurred when adding a question. Please try again later.
zsmall.productQA.textQuestionsError=Please fill in the new question information text.
zsmall.productQA.additionalQuestionsError=You can only reply in your own question thread.
zsmall.productQA.notExistQuestionsError=The question no longer exists.
zsmall.productQA.editAnswersError=Unknown error occurred when editing question. Please try again later.
zsmall.productQA.notExistAnswersError=Answer does not exist or has been closed.
zsmall.productQA.deleteAnswersError=Unknown error occurred when deleting reply. Please try again later.
zsmall.productQA.deleteQuestionsError=Unknown error occurred when deleting question. Please try again later.
zsmall.productQA.notQuestionsError=Failed to delete, the object is not question information.
zsmall.productQA.queryProductNotFound=Product or its specification does not exist while getting question list.
zsmall.productQA.continueQuestionsError=An unknown error occurred when adding continue question information.
zsmall.productQA.continueQuestionsAgainError=Please wait for merchant's reply before asking the next question in the same thread.
zsmall.productQA.textAnswersError=Please fill in the reply message text.
zsmall.productQA.getLogEditReplyError=Unknown error occurred while getting editing log. Please try again later.
zsmall.productQA.replyNotEditError=The reply has not been edited currently.
zsmall.productQA.productSkuCodeIsnullError=ProductSkuCode cannot be empty.
zsmall.productQA.reportQuestionError=Unknown error occurred when reporting the question. Please try again later.
zsmall.productQA.closedQuestionsError=The question no longer exists.
zsmall.productQA.notExistAnswers2Error=Answer does not exist.
zsmall.productQA.notExistContinueQuestionsError=Questioning does not exist.
zsmall.productQA.questionCodeIsBlank=QuestionCode cannot be empty.
zsmall.productQA.answerCodeIsBlank=AnswerCode cannot be empty.
zsmall.productQA.answerTypeIsBlank=AnswerType cannot be empty.

#\u535A\u5BA2\u76F8\u5173
zsmall.blog.createBlogArticleError=An unknown error occurred when create blog article.
zsmall.blog.queryBlogArticlePageError=Unknown error occurred when getting blog information. Please try again later.
zsmall.blog.queryBlogArticleDetailError=Unknown error occurred when getting blog article information. Please try again later.
zsmall.blog.blogArticleNotExist=Blog article not exist or deleted.
zsmall.blog.updateBlogArticleError=An unknown error occurred when update blog article.
zsmall.blog.deleteBlogArticleError=An unknown error occurred when delete blog article.
zsmall.blog.queryBlogTypeError=Unknown error occurred when getting blog type information. Please try again later.
zsmall.blog.category.code.duplication=Category code duplication.
zsmall.blog.category.multilingual.notjson=Multilingual names must be in JSON format.

#\u7269\u6D41\u6A21\u7248
zsmall.logisticsTemplate.getShippingServiceError=Exception occurred when query list information of shipping service providers.
zsmall.logisticsTemplate.addShippingServiceError=An unknown error occurred when adding shipping service provider information.
zsmall.logisticsTemplate.shippingNameCanNotNull=shipper can not be blank.
zsmall.logisticsTemplate.shippingNameCanNotRepeat=Logistics Name can not repeat.
zsmall.logisticsTemplate.addLogisticsTemplateError=Unknown error occurred when adding new logistics template. Please try again later or contact customer support.
zsmall.logisticsTemplate.getRateTypeError=Unknown error occurred when getting rates information. Please try again later or contact customer support.
zsmall.logisticsTemplate.getCountryError=Unknown error occurred when getting province/city information. Please try again later or contact customer support.
zsmall.logisticsTemplate.getWeightUnitError=Unknown error occurred when getting weight information.
zsmall.logisticsTemplate.getWarehouseError=An unknown error occurred when query warehouse information.
zsmall.logisticsTemplate.getLogisticsTemplateListError=Unknown error occurred when getting the logistics template list. Please try again later or contact customer support.
zsmall.logisticsTemplate.deleteLogisticsTemplateError=Unknown error occurred when deleting the logistics template.
zsmall.logisticsTemplate.logisticsTemplateNotExistError=Logistics template does not exist.
zsmall.logisticsTemplate.logisticsTemplateUpdateError=An unknown error occurred when update logistics template.
zsmall.logisticsTemplate.logisticsTemplateBelongError=You can only modify your own logistics template.
zsmall.logisticsTemplate.updateLogisticsTemplateServiceAliasError=Exception occurred when updating service name information.
zsmall.logisticsTemplate.updateLogisticsTemplateShipToError=Exception occurred when updating logistic template ship to information.
zsmall.logisticsTemplate.logisticsTemplateNameDuplicateError=Name of logistics template must be unique.
zsmall.logisticsTemplate.delLogisticsTemplateItemError=An unknown error occurred when delete logistics template item.
zsmall.logisticsTemplate.getLogisticsTemplateDetailError=An unknown error occurred when query logistics template detail
zsmall.logisticsTemplate.zipCodeDoesNotMatchCity=Zip code does not match city.
zsmall.logisticsTemplate.logisticsFeeError=An unknown error occurred when freight calculation
zsmall.logisticsTemplate.targetCityNotSupportError=Delivery to the target city is not supported.
zsmall.logisticsTemplate.weightUnitError=The weight unit is not supported.
zsmall.logisticsTemplate.warehouseNotExistError=Selected warehouse does not exist.
zsmall.logisticsTemplate.logisticsTemplateRateRuleLessThanOneError=Keep at least one rate rule.
zsmall.logisticsTemplate.logisticsTemplateShipToNotInputError=Please select ship to.
zsmall.logisticsTemplate.logisticsTimeInputError=Time is Not Less Than One Day.
zsmall.logisticsTemplate.logisticsTimeInput2Error=Delivery maximum time must be greater than or equal to minimum time.
zsmall.logisticsTemplate.logisticsNumOrFeeInputError=The quantity and amount cannot be negative.
zsmall.logisticsTemplate.logisticsTemplateAssociatedProduct=The logistics template has associated product and cannot be deleted.


#Marketplace\u76F8\u5173
zsmall.marketplace.callGoogleMapApiTimeOutError=Timeout error occurred when call google maps distance matrix interface
zsmall.marketplace.callGoogleMapApiFailError=Failed to call google maps distance matrix interface
zsmall.marketplace.callGoogleMapApiNotSupportError=Google maps distance matrix interface does not support querying distance information
zsmall.marketplace.callGoogleMapApiUnknownError=Google maps distance matrix interface unknown error.
zsmall.marketplace.callGoogleMapGeocodingApiFailError=Failed to call google maps geocoding interface
zsmall.marketplace.callGoogleMapGeocodingApiTimeOutError=Timeout error occurred when call google maps distance matrix interface
zsmall.marketplace.getMarketplaceProductDetailForDetailError=An exception occurred when query details of Marketplace products (for Shopify).
zsmall.marketplace.downloadProductZipError=An unknown error occurred when download product zip.


#\u9000\u8D27\u653F\u7B56\u6A21\u677F\u76F8\u5173
zsmall.shippingReturns.shippingReturnsNotExist=Shipping & return policy not exist.
zsmall.shippingReturns.shippingReturnsCreateError=An unknown error occurred when create information of logistic & return policy template.
zsmall.shippingReturns.shippingReturnsUpdateError=An unknown error occurred when update information of logistic & return policy template.
zsmall.shippingReturns.shippingReturnsDeleteError=An unknown error occurred when delete information of logistic & return policy template.
zsmall.shippingReturns.shippingReturnsBelongError=An unknown error occurred when the return policy template does not belong to the current user.
zsmall.shippingReturns.shippingReturnsQueryError=An unknown error occurred when create information of logistic & return policy template.
zsmall.shippingReturns.shippingReturnsQueryDetailError=An unknown error occurred when create information of logistic & return policy template detail.
zsmall.shippingReturns.queryShippingReturnsListError=An unknown error occurred when query shipping & return policy.
zsmall.shippingReturns.reviewShippingReturnsError=An unknown error occurred when review shipping & return policy.
zsmall.shippingReturns.shippingReturnsContentIsBlank=Shipping & return policy content cannot be empty.

#\u83B7\u53D6\u8FDE\u63A5\u6E20\u9053\u5E97\u94FA\u53CA\u94FA\u8D27\u7684\u5DE5\u4F5C\u6307\u5357\u76F8\u5173\u5F02\u5E38
zsmall.workGuidelines.workGuideQueryError=Unknown error occurred when query work guide.

#\u4E0B\u8F7D\u4E2D\u5FC3
zsmall.downloadCenter.queryDownloadRecordError=An unknown error occurred when query download record.
zsmall.downloadCenter.deleteDownloadRecordError=An unknown error occurred when delete download record.
zsmall.downloadCenter.downloadRecordGenerating=Export records in the generation, please wait after completing.

#\u914D\u7F6E\u76F8\u5173\u5F02\u5E38
zsmall.configuration.homePropertiesTypeNotExistError=Home properties type not exist.
zsmall.configuration.homePropertiesAddError=Unknown error when saving the homepage properties.
zsmall.configuration.homePropertiesUpdateError=Unknown error when update the homepage properties.
zsmall.configuration.homePropertiesChannelExistingLengthOutError=Existing configuration has reached the maximum. No more configurations can be added.
zsmall.configuration.homePropertiesChannelComingSoonLengthOutError=Coming soon configuration has reached the maximum. No more configurations can be added.
zsmall.configuration.homePropertiesDeleteError=Unknown error when delete the home properties.
zsmall.configuration.homePropertiesDestTypeNotSupportError=This page does not support configuration.
zsmall.configuration.homePropertiesMarketplaceGetError=Unknown error when entering homepage.
zsmall.configuration.homePropertiesMarketplaceGetTypeNoExistError=Failed to read the homepage information correctly.
zsmall.configuration.homePropertiesGetError=Unknown error when querying homepage configuration.
zsmall.configuration.homePropertiesSaveError=Unknown error when saving the homepage properties.
zsmall.configuration.homeSearchProductsError=Unknown error occurred when search products. Please try again later.
zsmall.configuration.homePropertiesCnBannerRightAdError=The Banner right AD configuration must be 3.
zsmall.configuration.activityPropertiesCreateError=Unknown error occurred when add activity properties. Please try again later.
zsmall.configuration.activityNameCannotRepeat=The activity name cannot be repeated. Please try again later.
zsmall.configuration.activityPropertiesNotExists=The activity does not exist
zsmall.configuration.activityPropertiesDetailError=Unknown error occurred when query activity properties detail. Please try again later.
zsmall.configuration.activityPropertiesListError=Unknown error occurred when query activity properties list. Please try again later.
zsmall.configuration.activityPropertiesDeleteError=Unknown error occurred when delete activity properties list. Please try again later.
zsmall.configuration.activityPropertiesNotDeleteUsedError=The activity configuration has been used by the mall template. Please unbind first, template title: {0}
zsmall.configuration.homeBusinessParameterError=Unknown error when get home business parameter.


#\u6807\u7B7E\u76F8\u5173
zsmall.label.labelAddError=Unknown error when create the label.
zsmall.label.labelUpdateError=Unknown error when update the label.
zsmall.label.labelDeleteError=Unknown error when delete the label.
zsmall.label.labelQueryError=Unknown error when query the label.
zsmall.label.labelBindingError=Unknown error when product binding label.
zsmall.label.labelNotExistError=label does not exist.
zsmall.label.labelExistError=label already exist.
zsmall.label.labelProductExistError=products have been bound with labels.
zsmall.label.labelProductOneToOneError=Only one label can be bound to a product.
zsmall.label.labelUnbindError=Unknown error when product unbind label.


#\u5546\u54C1\u6E20\u9053\u7BA1\u63A7\u76F8\u5173
zsmall.channelControl.queryProductChannelControlPageError=An unknown error occurred when query product channel control page.
zsmall.channelControl.setProductChannelControlError=An unknown error occurred when set product channel control.
zsmall.channelControl.memberIdExceedLimit=Member ID quantity exceed limit(Max: {0}).
zsmall.channelControl.switchProductChannelControlTypeError=Unknown error occurred when switch product channel control type.


#\u94B1\u5305\u7BA1\u7406\u76F8\u5173\u5F02\u5E38
zsmall.walletManagement.rechargeQueryError=An unknown error occurred when query recharge.
zsmall.walletManagement.transactionsQueryError=An unknown error occurred when query transactions.
zsmall.walletManagement.toFreezeOrThawStoreWalletError=An unknown error occurred when to freeze or thaw store wallet.
zsmall.walletManagement.editExpireDateError=The time format is wrong
zsmall.walletManagement.editCardInformationError=An exception occurred while editing the card information


#\u8BA2\u5355\u6269\u5C55\u76F8\u5173\u5F02\u5E38
zsmall.orderExtend.recreateWmsOrderError=An unknown error occurred when recreate WMS sales order.
zsmall.orderExtend.orderNotExistOrWmsNotError=Order not exist or WMS sales order has no 'CreateFailed'.



#payoneer\u76F8\u5173\u5F02\u5E38
zsmall.payoneer.payoneerAuthorizationError=An unknown error occurred when Payoneer authorization.
zsmall.payoneer.payoneerAuthorizationExpired=Payoneer authorization operation has expired.
zsmall.payoneer.payoneerRegistrationConnectionError=An unknown error occurred when registration connection.
zsmall.payoneer.payoneerAccountBeenBoundError=The current Payoneer account has been bound.
zsmall.payoneer.payoneerBindingFailed=Payneer account binding failed.
zsmall.payoneer.payoneerAccountNameUpdateFailed=An unknown error occurred when update Payoneer account name.
zsmall.payoneer.payoneerDeleteFailed=An unknown error occurred when delete Payneer account.
zsmall.payoneer.payoneerQueryError=An unknown error occurred when query Payoneer account list.
zsmall.payoneer.payoneerQueryBalanceError=An unknown error occurred when query Payoneer balance list.
zsmall.payoneer.payoneerPaymentCommitError=An unknown error occurred when Payoneer payment.
zsmall.payoneer.payoneerEmptyError=Payoneer does not exist.
zsmall.payoneer.payoneerBalanceError=Current currency account does not exist in Payoneer balance list.
zsmall.payoneer.payoneerInsufficientBalanceError=Insufficient balance.
zsmall.payoneer.payoneerAccountLengthError=Payoneer account name cannot exceed 20.
zsmall.payoneer.payoneerApplicationNotRegisteredError=Payoneer Application is not registered, or active.
zsmall.payoneer.payoneerPaymentTimeoutError=Payoneer failed to pay, the request has expired.
zsmall.payoneer.payoneerPaymentFail=Payoneer failed to pay.
zsmall.payoneer.payoneerIdEmptyError=Payoneer accountId cannot be empty.
zsmall.payoneer.commitIdEmptyError=Payoneer commitId cannot be empty.


#\u4FC3\u9500\u6D3B\u52A8\u76F8\u5173\u5F02\u5E38
zsmall.productActivity.productActivityNotExist=The specified activity ({0}) does not exist or has ended.
zsmall.productActivity.productActivityQueryError=Unknown error occurred when querying activity.
zsmall.productActivity.activityStatusError=The system recognized an unknown activity status.
zsmall.productActivity.activityStatusNotDraftUnderreviewError=Activity status must be Draft or pending UnderReview.
zsmall.productActivity.activityStartTimeError=The event starts at least today.
zsmall.productActivity.activityTimeLessThen30Error=The duration of the activity is less than 30 days.
zsmall.productActivity.activityTypeNotMatch=Activity type does not match.
zsmall.productActivity.activityNotExist=Activity does not exist.
zsmall.productActivity.distributorsStockLockError=Unknown error occurred when stock lock.
zsmall.productActivity.stockLockSaveError=Unknown error occurred when save stock lock activity.
zsmall.productActivity.activityQuantityLessThenMinimum=The total quantity of activity products cannot be less than the minimum order quantity
zsmall.productActivity.stockLockQuantityNotEnough=The quantity does not enough:({0})
zsmall.productActivity.stockLockNotExistError=Failed to save the stock lock activity, because activity is not exist.
zsmall.productActivity.stockLockQueryDetailError=An unknown error occurred when query stock lock activity.
zsmall.productActivity.stockLockUpdateError=An unknown error occurred when update stock lock activity.
zsmall.productActivity.activityStatusNotExist=The activity status does not exist.
zsmall.productActivity.activityCannotUpdateStatus=Insufficient permissions to update to the target status.
zsmall.productActivity.stockLockClosed=Activity has been closed.
zsmall.productActivity.activityWarehouseNotExistError=Selected warehouse does not exist:[{0}]
zsmall.productActivity.activityAllQuantityNotEnough=The quantity does not enough.
zsmall.productActivity.productOffShelfErrorActivityNotEnd=The product off shelf fail, Please end the activity:[{0}] .
zsmall.productActivity.distributorApplyError=Distributor apply error
zsmall.productActivity.activityQuantityNotEnough=Activity insufficient inventory.
zsmall.productActivity.activityWarehouseNotExist=Warehouse not exist.
zsmall.productActivity.backInventoryNoSpecialWarehouse=No warehouse specified when returning inventory.
zsmall.productActivity.productDeleteErrorActivityNotEnd=The product delete fail, Please end the activity:[{0}] .
zsmall.productActivity.productDeleteErrorBulkActivityNotEnd=The product delete fail, Distributor activity not ended:[{0}] .
zsmall.productActivity.quantityLessMinimumQuantity=The current requirement quantity is less than the minimum quantity required by the activity.
zsmall.productActivity.activitySaveError=Unknown error occurred when creating activity.
zsmall.productActivity.activityStatusUpdateError=Unknown error occurred when update activity status.
zsmall.productActivity.productOffShelfErrorActivityItemNotEnd=The product off shelf fail, Distributor activity not ended:[{0}] .
zsmall.productActivity.cancelBulkActivityError=Unknown error occurred when cancel distributor's activity.
zsmall.productActivity.activityNotExistOrCancelled=Activity does not exist or has been cancelled.
zsmall.productActivity.refundAmountCannotBeGreaterThan=The refund amount cannot be greater than the total amount ${0}.

#\u4E70\u65AD\u76F8\u5173
zsmall.buyout.buyoutCreateError=Unknown error occurred when create buyout activity.
zsmall.buyout.queryBuyoutDetailError=Unknown error occurred when query buyout activity detail.
zsmall.buyout.distributorsBuyoutError=Unknown error occurred when distributor join buyout activity.
zsmall.buyout.queryBuyoutDraftError=Unknown error occurred when query buyout activity draft.


#\u6570\u636E\u7EDF\u8BA1
zsmall.statistics.summaryTypeError=Statistical type cannot be empty
zsmall.statistics.statisticSkuInfoError=Unknown error occurred when query product sku statistics details.
zsmall.statistics.statisticSkuBulkInfoError=Unknown error occurred when query product sku statistical details of distributors.
zsmall.statistics.statisticSkuSupInfoError=Unknown error occurred when query product sku statistical details of supplier.
zsmall.statistics.statisticSkuQueryError=Unknown error occurred when query product sku statistical list.
zsmall.statistics.statisticSkuBulkQueryError=Unknown error occurred when query product sku statistical list of distributors.
zsmall.statistics.statisticSkuSupQueryError=Unknown error occurred when query product sku statistical list of supplier.


#\u6E05\u8D27\u76F8\u5173
zsmall.liquidation.liquidationProductQueryError=Unknown error occurred when query liquidation product.
zsmall.liquidation.liquidationActivityQueryError=Unknown error occurred when query liquidation activity.
zsmall.liquidation.liquidationActivityNotExist=The liquidation activity does not exist.
zsmall.liquidation.liquidationOrderSaveError=Unknown error occurred when save liquidation order.
zsmall.liquidation.liquidationDeliveryMethodNotExist=The liquidation activity delivery method does not exist.
zsmall.liquidation.liquidationQuantityNotEnough=The quantity does not enough
zsmall.liquidation.liquidationQuantityAndPriceNotMatch=The quantity does not match the price range:({0})
zsmall.liquidation.liquidationActivitySaveError=Failed to save the liquidation activity information.
zsmall.liquidation.liquidationActivityDetailError=Unknown error occurred when query liquidation activity Details.
zsmall.liquidation.liquidationOrderQueryError=Unknown error occurred when query liquidation orders.
zsmall.liquidation.liquidationOrderBalanceError=Unknown error occurred when query Order balance details.
zsmall.liquidation.liquidationOrderFeeSettingError=Unknown error occurred in order fee setting.
zsmall.liquidation.liquidationStatusUpdateError=Unknown error occurred when update liquidation activity status.
zsmall.liquidation.liquidationOrderNotExist=The liquidation order does not exist.
zsmall.liquidation.liquidationOrderStatusUpdateError=Unknown error occurred when update liquidation activity order status.
zsmall.liquidation.liquidationOrderDeductInventoryError=Unknown error occurred when deduct inventory.
zsmall.liquidation.liquidationProductSkuNotEmpty=Product cannot be empty for The liquidation activity.
zsmall.liquidation.liquidationProductImageNotEmpty=Product image cannot be empty for The liquidation activity.
zsmall.liquidation.liquidationProductAttributeNotEmpty=Product price and quantity range of The liquidation activity cannot be empty.
zsmall.liquidation.liquidationProductInventoryQuantityError=Product price cannot be empty for The liquidation activity.
zsmall.liquidation.liquidationOrderParamError=Order status parameter error of The liquidation activity.
zsmall.liquidation.liquidationOrderLessThenMinimumQuantity=The actual purchase quantity is less than the minimum purchase quantity.
zsmall.liquidation.liquidationWarehouseCodeEmpty=The warehouse code is empty
zsmall.liquidation.liquidationProductNameEmpty=The product name is empty
zsmall.liquidation.liquidationUploadProductSkuFileError=It is not legal to upload clearance product files
zsmall.liquidation.liquidationPalletSizeIncomplete=The pallet size data is not complete
zsmall.liquidation.liquidationDeliveryMethodEmpty=The delivery method is empty
zsmall.liquidation.liquidationPickUpOperationFeeEmpty=The pick up operation fee is empty
zsmall.liquidation.liquidationDeliveryToWarehouseOperationFeeEmpty=The delivery to warehouse operation fee is empty
zsmall.liquidation.liquidationPalletExpectedSpaceEmpty=Pallet space is expected to be empty
zsmall.liquidation.liquidationPalletFeeEmpty=The pallet fee is empty
zsmall.liquidation.liquidationProductSkuPropertiesIncomplete=The product sku properties are incomplete
zsmall.liquidation.liquidationProductSkuSizeIncomplete=The product sku size are incomplete
zsmall.liquidation.liquidationProductSkuQuantityEmpty=The number of product SKUs is empty
zsmall.liquidation.liquidationSkuUploadParamCannotEmpty=The required information\uFF08{0}\uFF09 for liquidation activity product cannot be blank.
zsmall.liquidation.liquidationOrderPalletRemarkEmpty=Setting up a pallet fee requires a remark
zsmall.liquidation.liquidationOrderSetShippingFeeError=The pick up order cannot set shipping fee
zsmall.liquidation.liquidationSkuNumberFormatError=The format of the number value of the uploaded liquidation activity product is wrong.
zsmall.liquidation.liquidationProductMinimumQuantityEmpty=The minimum quantity of products is empty
zsmall.liquidation.liquidationProductSkuPriceInformationIncomplete=Product sku price information is incomplete
zsmall.liquidation.liquidationSaveSellingMethodError=An error occurred when saving the selling method
zsmall.liquidation.liquidationProductSkuFileError=An unknown error occurred when obtaining the cleared merchandise file
zsmall.liquidation.liquidationProductSkuQuantityRangesError=The quantity(price) range of product in the liquidation activity is incorrect
zsmall.liquidation.liquidationQuantityNotMatchPriceError=There is no matching price for the quantity of liquidation activity products


#\u8D26\u5355\u76F8\u5173
zsmall.bill.firstGenerateBillError=Unknown error occurred when first generate bill.
zsmall.bill.queryBillPageError=Unknown error occurred when query bill list.
zsmall.bill.queryBillDetailError=Unknown error occurred when query bill detail.
zsmall.bill.billNotFound=No bill found.
zsmall.bill.exportBillListError=Unknown error occurred when export bill list.
zsmall.bill.generateSealBillError=Unknown error occurred when generate seal bill.
zsmall.bill.queryBillClassError=Unknown error occurred when query bill class list.
zsmall.bill.downloadBillPdfError=Unknown error occurred when download bill pdf.
zsmall.bill.downloadBillPdfNotGenerated=The bill PDF has not been generated, please confirm whether the bill has been settled.
zsmall.bill.downloadBillPdfGenerating=The bill PDF is being generated, please try again later.
zsmall.bill.downloadBillPdfGeneratedFailed=Failed to generate bill PDF. Please contact the administrator to regenerate.
zsmall.bill.supplementBillAbstractError=Unknown error occurred when supplement bill abstract.
zsmall.bill.billingFailureUnknownUser=Billing failure, unknown user.
zsmall.bill.billingFailureUnknownAbstractType=Billing failure, unknown abstract type.
zsmall.bill.billingFailureUnknownRelationType=Billing failure, unknown relation type.
zsmall.bill.billingFailureNoBillFound=Billing failure, no bill found.
zsmall.bill.billingFailureOrderPriceFound=Billing failure, price fields not found on order.


#\u6536\u6B3E\u8D26\u6237\u76F8\u5173
zsmall.receiptAccount.receiptAccountSaveError=Unknown error occurred when save account.
zsmall.receiptAccount.receiptAccountQueryError=Unknown error occurred when query account.
zsmall.receiptAccount.receiptAccountDisabledError=Unknown error occurred when disabled account.
zsmall.receiptAccount.receiptAccountNotExistOrDisabledError=Receipt account does not exist or disabled.
zsmall.receiptAccount.receiptAccountGreaterThan10Error=Receipt account greater than 10.
zsmall.receiptAccount.receiptAccountLengthNotEnoughError=Account length not enough.
zsmall.receiptAccount.receiptEmailFormatError=Email format error.
zsmall.receiptAccount.receiptAccountRepeatError=The same bank card number cannot be added to the same country.
zsmall.receiptAccount.receiptAccountPayoneerEmailRepeatError=Payoneer email repeat.
zsmall.receiptAccount.receiptAccountCreditNull=Receipt bank account information cannot be empty.
zsmall.receiptAccount.receiptAccountPayoneerNull=Receipt payoneer account information cannot be empty.
zsmall.receiptAccount.receiptAccountCreditParameterEmpty=Receipt bank account information cannot be empty.
zsmall.receiptAccount.receiptAccountPayoneerParameterEmpty=Receipt payoneer account information cannot be empty.


#\u63D0\u73B0\u76F8\u5173\u5F02\u5E38
zsmall.receiptRecord.receiptRecordQueryError=Unknown error occurred when query receipt record.
zsmall.receiptRecord.receiptAmountLessThanZeroError=Receipt amount must greater than 0.
zsmall.receiptRecord.receiptAmountNotFoundError=Receipt account does not not found.
zsmall.receiptRecord.receiptRecordNotFoundError=Receipt record does not not found.
zsmall.receiptRecord.receiptRecordSaveError=Unknown error occurred when save receipt record.
zsmall.receiptRecord.receiptRecordUpdateError=Unknown error occurred when update receipt record.
zsmall.receiptRecord.receiptAmountNotMatch=The withdrawal amount does not match.
zsmall.receiptRecord.receiptTransactionRecordSaveError=Failed to add billing withdrawal transaction record relationship information!
zsmall.receiptRecord.billReceiptStateUpdateError=Failed to modify bill withdrawal status information!


#\u652F\u4ED8\u7BA1\u7406\u76F8\u5173
zsmall.payManagement.settingPaymentPasswordError=An error occurred while setting the payment password
zsmall.payManagement.paymentPasswordNotSet=No payment password is set
zsmall.payManagement.changePaymentSettingError=An error occurred while modifying the payment password settings
zsmall.payManagement.queryPaymentSettingError=An error occurred while querying payment settings
zsmall.payManagement.verifyPaymentPasswordError=An exception occurred while verifying the payment password
zsmall.payManagement.verifyPaymentSettingError=An exception occurred while verifying payment Settings
zsmall.payManagement.paymentPasswordEmpty=The payment password is empty
zsmall.payManagement.incorrectPaymentPassword=Incorrect payment password


#\u5206\u9500\u5546\u3001\u4F9B\u5E94\u5546\u5165\u9A7B\u76F8\u5173
zsmall.settled.contactUserNameNotEmpty=Business contact is required
zsmall.settled.phoneNumberNotEmpty=PhoneNumber is required
zsmall.settled.emailNotEmpty=Email is required
zsmall.settled.firstNameNotEmpty=First name is required
zsmall.settled.lastNameNotEmpty=Last name is required
zsmall.settled.instantMsgAppTypeNotEmpty=Instant messaging software account is required
zsmall.settled.instantMsgAppNumberNotEmpty=Instant messaging software account is required
zsmall.settled.hasCompanyNotEmpty=Whether there is a company body cannot be empty
zsmall.settled.companyNameNotEmpty=Company name is required
zsmall.settled.mainCategoriesNotEmpty=Main categories is required
zsmall.settled.teamSizeNotEmpty=Team size is required
zsmall.settled.companyStateIdNotEmpty=The state/province id of the company cannot be empty
zsmall.settled.companyCityTextNotEmpty=The city where the company is located cannot be empty
zsmall.settled.companyContactAddressNotEmpty=Company contact address is required
zsmall.settled.recentAnnualSalesScaleNotEmpty=Annual sales scale in the past year is required
zsmall.settled.cecExperienceNotEmpty=Cross-border e-commerce experience is required
zsmall.settled.otherExperienceNotEmpty=Other online sales channels experience is required
zsmall.settled.userSupSettleInSaveError=Unknown error occurred when saving the supplier's information.
zsmall.settled.userSupReviewRecordQueryError=Unknown error occurred when query the information of employee audit supplier entry record.
zsmall.settled.userSupSettleInQueryContactError=Unknown error occurred when query the contact information of supplier company.
zsmall.settled.userSupSettleInQueryBasicError=Unknown error occurred when query the basic information of supplier.
zsmall.settled.userSupSettleInQueryError=Unknown error occurred when query the supplier's enrollment information.
zsmall.settled.userSupSettleInUpdateContactError=Unknown error occurred when update contact information of supplier company.
zsmall.settled.userSupSettleInUpdateBasicError=Unknown error occurred when update the basic information of supplier.
zsmall.settled.userBulkInfoSaveError=Unknown error occurred when saving the distributor's information.
zsmall.settled.userBulkInfoQueryError=Unknown error occurred when query the information of distributor.
zsmall.settled.userSupReviewRecordUpdateError=Unknown error occurred when update the information of employee audit supplier entry record.
zsmall.settled.userSupIsPerfectInfoStatusError=Unknown error occurred when obtaining the status of supplier Supplier entry information.
zsmall.settled.userBulkInformationIsNotPerfect=Distributor onboarding information is not perfect
zsmall.settled.userSupSettleInToBeReviewed=In the process of supplier registration information review, please do not resubmit.
zsmall.settled.userSupSettleInNotExists=The supplier's entry information does not exist.


#\u5458\u5DE5\u8D44\u91D1\u7BA1\u7406\u76F8\u5173\u5F02\u5E38
zsmall.fundManagement.queryFundsOverviewStatisticsError=An error occurred while querying summary statistics
zsmall.fundManagement.queryFundFlowError=An error occurred while querying fund flow
zsmall.fundManagement.exportFundsOverviewError=An error occurred while exporting the fund flow



#\u56FD\u5185\u73B0\u8D27\u76F8\u5173
zsmall.chinaSpotProduct.queryChinaSpotProductListError=An error occurred when query china spot product list.
zsmall.chinaSpotProduct.uploadChinaSpotProductExcelError=An error occurred when upload china spot product excel.
zsmall.chinaSpotProduct.queryChinaSpotProductDetailError=An error occurred when query china spot product detail.
zsmall.chinaSpotProduct.queryChinaSpotProductNotExist=China spot product not exist.
zsmall.chinaSpotProduct.deleteChinaSpotProductError=An error occurred when delete china spot product list.
zsmall.chinaSpotProduct.exportChinaSpotProductListError=An error occurred when export china spot product list.


#\u56FD\u5916\u73B0\u8D27\u6279\u53D1\u5546\u54C1\u76F8\u5173\u5F02\u5E38
zsmall.wholesaleProduct.wholesalePriceSetupError=The wholesale price setting is not standardized.
zsmall.wholesaleProduct.wholesaleTieredPriceNotExist=The wholesale commodity ladder price does not exist.
zsmall.wholesaleProduct.wholesaleSkuPriceNotExist=SKU price of wholesale products does not exist.
zsmall.wholesaleProduct.wholesaleDeliveryTypeNotExist=Wholesale products shipment type does not exist.
zsmall.wholesaleProduct.wholesaleInventoryError=The total inventory of wholesale products cannot be less than the minimum order quantity.
zsmall.wholesaleProduct.wholesaleSkuRepeat=SKU or UPC \uFF08{0}\uFF09 already exists or is duplicate.
zsmall.wholesaleProduct.homeSearchWholesaleProductsError=Unknown error occurred when search wholesale products. Please try again later.
zsmall.wholesaleProduct.wholesaleSkuNotExist=Wholesale products do not exist, please try again later.
zsmall.wholesaleProduct.wholesaleCannotAddToCart=This item cannot be added to the shopping cart.
zsmall.wholesaleProduct.hasIntentionError=Product[{0}] has an intention form and cannot be deleted
zsmall.wholesaleProduct.cancelOrderSuccess=Cancel wholesale order success
zsmall.wholesaleProduct.wholesaleUpdatePriceSuccess=Saved successfully. The price change has been submitted for review. Please go to the [Product Review] menu to view the review results.
zsmall.wholesaleProduct.zsmallDropshippingPaySuccess=Payment success! We will contact you within 48 hours to assist you in shipping to your customers
zsmall.wholesaleProduct.wholesalePlaceOrderError=An unknown error occurred when place order.
zsmall.wholesaleProduct.minimumQuantityNotReached=The minimum order quantity is not reached.
zsmall.wholesaleProduct.productSkuInsufficientInventory=Item No.[{0}] insufficient inventory!
zsmall.tiktok.packageNotExists = Tiktok order: [{0}] ,Package information is incomplete!
zsmall.tiktok.packageUrlNotExists = Tiktok order: [{0}] ,Package URL is empty!
zsmall.tiktok.trackingNoNotExists = Tracking no is empty!
zsmall.wholesaleProduct.shippingAddressNotExist=The selected shipping address does not exist or has been deleted. Please try again.
zsmall.wholesaleProduct.enterPriceError=An unknown error occurred when enter price.
zsmall.wholesaleProduct.wholesaleOrderNotExist=Wholesale order not exist.
zsmall.wholesaleProduct.notNeedEnterPrice=The current order status does not need to enter a price.
zsmall.wholesaleProduct.wholesaleCancelOrderError=An unknown error occurred when cancel order.
zsmall.wholesaleProduct.operationCannotBePerformed=The operation cannot be performed in the current order status.
zsmall.wholesaleProduct.queryWholesaleOrderError=An unknown error occurred when query order detail.
zsmall.wholesaleProduct.queryWholesaleOrderPageError=An unknown error occurred when query order list.
zsmall.wholesaleProduct.logisticsMethodNotSupported=The selected logistics method is not supported.
zsmall.wholesaleProduct.incompleteLogisticsInformation=Incomplete logistics information.
zsmall.wholesaleProduct.pleaseUploadShippingLabel=Please upload the shipping label.
zsmall.wholesaleProduct.placeOrderPayBalance=An unknown error occurred when placing an order to pay the balance.
zsmall.wholesaleProduct.cannotImportToSalesChannel=This product cannot import to third sales channel.


#Excel\u5BFC\u5165\u9519\u8BEF\u4FE1\u606F
zsmall.excelImport.messageHead=Row[{0}]-Column[{1}]:
zsmall.excelImport.messageHeadNoColumn=Row[{0}]:
zsmall.excelImport.required=Required!
zsmall.excelImport.countryCodeNotExist = [{0}],The country code does not exist!
zsmall.excelImport.illegalArgument=The cell value does not meet the requirements!
zsmall.excelImport.integerGteZero=Must be a positive integer greater than 0.
zsmall.excelImport.illegalDropShippingQuantity=A dropshipping inventory can only be equal to 0 or equal to self pickup inventory!

zsmall.excelImport.skuNotExist=The product does not exist (If the [Warehouse ID] is filled in, it is also possible that the product is not stored in the specified warehouse)!
zsmall.excelImport.irregularity=Invalid value!
zsmall.excelImport.channelNotExist=This channel is not currently supported!
zsmall.excelImport.channelOrderNumberCannotBeEmpty = For orders with LTL as the carrier, the channel tracking number must be filled in!
zsmall.excelImport.country=Unsupported countries.
zsmall.excelImport.state=Must be 2-letter codes used by the United States Postal Service.
zsmall.excelImport.carrierCode=Must be UPS or FedEx.
zsmall.excelImport.trackingFormat=The maximum length of a Tracking No. is 22 digits, if you have multiple Tracking No., please separate them whit ';'.
zsmall.excelImport.zipCode=Non-compliant with specifications(00000 or 00000-0000). If the Excel format is incorrect, please download the import template again.
zsmall.excelImport.shippingType=Only 'Pick Up' or 'DropShipping' can be filled in.
zsmall.excelImport.dropShippingLimit=If [Shipping] selected the 'DropShipping', [Carrier], [Shipping Service], [Tracking No], [Third Billing], [Carrier Account], [Carrier Account Zip Code] cannot be filled in.
zsmall.excelImport.pickUpCarrier=If [Shipping] selected the 'Pick Up', then this item is required.
zsmall.excelImport.thirdBillingTrackingNo=If [Third Billing] selected the 'No', then this item is required.
zsmall.excelImport.pickUpThirdBilling=If [Shipping] selected the 'Pick Up', then either 'Yes' or' No 'must be selected.
zsmall.excelImport.thirdBillingCA=If [Third Billing] selected the 'Yes', then this item is required.
zsmall.excelImport.thirdBillingCAZipCode=If [Third Billing] selected the 'Yes', then this item is required.
zsmall.excelImport.phoneNumberLength=The length of the phone number cannot exceed 20 digits.
zsmall.excelImport.notThirdBillingTrackingNo=If [Third Billing] selected the 'Yes', Then this item needs to be blank.
zsmall.excelImport.notThirdBillingCA=If [Third Billing] selected the 'No', Then this item needs to be blank.
zsmall.excelImport.notThirdBillingCAZipCode=If [Third Billing] selected the 'No', Then this item needs to be blank.
zsmall.excelImport.thirdBillingNotSupportWarehouseCode=This warehouse does not support the use of Third Billing.
zsmall.excelImport.repeatStoreOrderID=Repeat!
zsmall.excelImport.itemNoNotExist=The product does not exist.
zsmall.excelImport.dropShippingOnly=This product only supports 'DropShipping'.
zsmall.excelImport.pickUpOnly=This product only supports 'Pick Up'.
zsmall.excelImport.activityNotExist=The product does not participate in the specified activity or the activity is closed.
zsmall.excelImport.unrecognizableTrackingNo=The carrier cannot be identified by the Tracking No.
zsmall.excelImport.itemNoNotJoinActivity=The product does not participate in the specified activity or the activity is closed.
zsmall.excelImport.priceRuleNoExist=System unknown error, please contact the administrator(E10028).

zsmall.excelImport.noProductDispatched=There are no product that can be dispatched in the order. Please check the order information.
zsmall.excelImport.confirmQuantityGtUndispatched=Confirm that the number of Excel lines shipped is greater than the number of product orders to be shipped.
zsmall.excelImport.channelStoreNotFound=Channel store not found.
zsmall.excelImport.duplicateMappingSku=Sku Duplicate mapping sku are not allowed in the same store!
zsmall.excelImport.itemNoDoesNotExist=Item No. does not exist and cannot be mapped.
zsmall.excelImport.carrierConfigurationAbnormal=Carrier configuration is abnormal.
zsmall.excelImport.walmartChannelNotSupportAmsp=The Walmart channel does not support the AMSP service.
zsmall.excelImport.multipleCurrenciesAreNotSupported = The template only supports the import of orders in the same currency. Please import multi-currency orders separately.

zsmall.excelImport.skuIdPriceSitePriceNotExist = SKU country prices do not exist.
zsmall.excelImport.trackingCannotBeEmpty = Tracking No. cannot be empty.
zsmall.excelImport.orderNoCannotBeEmpty=The order number cannot be empty.
zsmall.excelImport.orderItemNotExist=The suborder does not exist.
zsmall.excelImport.addressNotExist=The address information does not exist.
zsmall.excelImport.logisticsNotExist=The logistics information does not exist.
zsmall.excelImport.productSkuNotExist = The product SKU does not exist.
zsmall.excelImport.productSkuCannotBeEmpty = The product SKU ID cannot be empty.
zsmall.excelImport.trackingTooLong = The maximum length of tracking is 50.
zsmall.excelImport.carrierTooLong = The maximum length of carrier is 50.
zsmall.excelImport.trackingPickUpOnly = The tracking Upload is only supported for 'Pick Up' orders.
zsmall.excelImport.channelOrderNoTooLong = The maximum length of channelOrderNo is 50.
zsmall.excelImport.nonValidCarrier=The carrier is not valid.

zsmall.excelImport.itemNoRepeat=Item No. is repeat.
zsmall.product.productSiteNotExist =The product site does not exist.

zsmall.excelImport.priceGreaterZero=The price must be a number greater than 0.00.
zsmall.excelImport.productPriceNotChange=The price of the product has not changed.
zsmall.excelImport.productPriceFormatError=Product amount format error.

#\u56FD\u5185\u73B0\u8D27\u5BFC\u5165\u76F8\u5173
zsmall.excelImport.spot.required=Required
zsmall.excelimport.spot.productNameCharLimit=This character 160 characters limit!
zsmall.excelimport.spot.quantityError=The quantity value must be greater than 0 and less than 9 digits
zsmall.excelImport.spot.categoryNotExist=Category not exist.
zsmall.excelImport.spot.onlyEnglish=Only input english!
zsmall.excelImport.spot.invalidImageLink=Invalid image link. Make sure the link suffix is jpg, png, svg, jpeg and other common image formats.
zsmall.excelImport.spot.skuSizeError=SKU size value cannot be greater than 9 digits



#\u65E7\u54CD\u5E94\u4FE1\u606F

zsmall.user.userEmailOccupied=Mailbox is already occupied.
zsmall.user.userEmailVerifyCodeNotMismatch=Token Link has expired.
zsmall.user.userEmailSendError=An exception occurred when sending verification email.
zsmall.user.userEmailRedirectError=An exception occurred when verification email redirection.
zsmall.user.userSendSmsOtpError=An exception occurred when send SMS verification code.
zsmall.user.userSendSmsOtpVerifyError=An exception occurred when check SMS verification code.
zsmall.user.userPhoneNumberOccupied=The phone number has been taken.
zsmall.user.userSmsVerifyCodeNotMismatch=SMS verification code does not match or expires
zsmall.user.userSubmitBaseInfoError=An exception occurred when submit registration information.
zsmall.user.userLoginError=Login exception.
zsmall.user.userLoginTypeUnknown=Unknown login method.
zsmall.user.userAccountPasswordError=User account/password error.
zsmall.user.userOperateTypeUnknown=Unknown operation mode.
zsmall.user.userGetBaseInfoError=An exception occurred when obtaining basic user information.
zsmall.user.userNotLogin=User information does not exist, please log in again.
zsmall.user.userOldPasswordMismatch=The original password does not match.
zsmall.user.userChangePasswordError=An exception occurred when changing the password.
zsmall.user.userUpdateBaseInfoError=An exception occurred when update user basic information.
zsmall.user.userResetPasswordError=An exception occurred when password reset.
zsmall.user.userUpdateUsernameError=An exception occurred when update user account.
zsmall.user.userUploadFileIsNull=Upload file cannot be empty.
zsmall.user.userUploadAvatarError=An exception occurred when user uploading avatar.
zsmall.user.userDeleteAvatarError=An exception occurred when user deletes avatar.
zsmall.user.userLogoutError=Logout exception.
zsmall.user.userNotConnectionStore=User is not associated with a store.
zsmall.user.userStoreNotActive=User store is not activated.
zsmall.user.userEmailNotExist=Email does not exist.
zsmall.user.userAddAccessRecordError=An exception occurred when increase user access record.
zsmall.user.userInfoNotExist=User information does not exist.
zsmall.user.checkUserLife=Check if the user is online.
zsmall.user.userShippingAddressNotExist=The user's delivery address does not exist.
zsmall.user.getUserShippingAddressError=An exception occurred when obtaining the user's delivery address information.
zsmall.user.addUserShippingAddressError=An exception occurred when add user receiving address information.
zsmall.user.deleteUserShippingAddressError=An exception occurred when user deletes the delivery address.
zsmall.user.updateUserShippingAddressTypeError=An exception occurred when user set default delivery address.
zsmall.user.getStoreBaseConfigurationInfoError=An exception occurred when getting user store basic configuration information.
zsmall.user.setStoreBaseConfigurationInfoError=An exception occurred when setting store base configuration info.
zsmall.user.updateDefaultLanguageError=An exception occurred when updating the user's default language.
zsmall.user.getPrivacyPolicyError=An exception occurred when getting privacy policy text.
zsmall.user.requestTypeIllegal=Illegal type.
zsmall.user.privacyPolicyNotExist=Privacy policy does not exist.
zsmall.user.googleVerificationError=Verification failed. Please try again.
zsmall.user.exceptionUnauthorized=Please log in first!
zsmall.user.exceptionLoginError=Failed to bind plaid ACH.
zsmall.user.requestParamNull=Required parameter cannot be empty.
zsmall.user.businessUserMobileNotexists=Mailbox is already occupied.
zsmall.user.businessUserMobileIncorrect=Token Link has expired.
zsmall.user.businessUserVerifycodeNotexists=An exception occurred when sending verification email.
zsmall.user.businessUserVerifycodeIncorrect=An exception occurred when verification email redirection.
zsmall.user.businessUserPwdError=An exception occurred when send SMS verification code.
zsmall.user.businessUserNotexists=An exception occurred when check SMS verification code.
zsmall.user.businessUserPwdIncorrect=The phone number has been taken.
zsmall.user.businessUserLoginException=SMS verification code does not match or expires
zsmall.user.businessUserRegisterException=An exception occurred when submit registration information.
zsmall.user.businessUserLoginNoauth=Login exception.
zsmall.user.businessUserIsexists=Unknown login method.
zsmall.user.businessUserNologin=User account does not exist.
zsmall.user.businessUserRealNameAlready=User account/password error.
zsmall.user.businessUserRealNameFail=Unknown operation mode.
zsmall.user.businessUserIdCardnoIllegal=An exception occurred when obtaining basic user information.
zsmall.user.businessUserInBlakelistLimitlogin=User information does not exist, please log in again.
zsmall.user.businessUserInBlakelistLimittrade=The original password does not match.
zsmall.user.businessUserRealNameNotexists=An exception occurred when changing the password.
zsmall.user.businessUserNameNotexists=An exception occurred when update user basic information.
zsmall.user.businessUserNameMismatch=An exception occurred when password reset.
zsmall.user.businessUserGetinfoError=An exception occurred when update user account.
zsmall.user.businessUserUpdateinfoError=Upload file cannot be empty.
zsmall.user.businessUserUpdatepwdError=An exception occurred when user uploading avatar.
zsmall.thirdParty.tpaGetLoginUrlError=Obtaining the third-party authorized login address exception.
zsmall.thirdParty.tpaLoginError=Third-party login exception.
zsmall.thirdParty.tpaGoogleLoginError=Google login exception.
zsmall.thirdParty.tpaAccountNotExist=The third-party account is not bound.
zsmall.thirdParty.tpaAccountError=The third-party account exception, please contact the administrator
zsmall.thirdParty.tpaGetInfoError=Obtaining third-party account information exception please contact the administrator.
zsmall.thirdParty.tpaConnectError=Third-party account authorization exception, please contact the administrator.
zsmall.thirdParty.tpaBindUserError=The third-party account binding user exception, please contact the administrator.
zsmall.thirdParty.tpaAlreadyBindAnotherUser=The third-party account has been bound to the user and cannot be bound again.
zsmall.thirdParty.tpaAccountUnbindError=The third-party account unbinding exception, please contact the administrator.
zsmall.thirdParty.tpaPlatformAlreadyBind=You have been bound to this third-party platform account and cannot be bound again.
zsmall.thirdParty.authenticationFailed=Third-party sales channel authorization login failed, please contact the platform administrator.
zsmall.thirdParty.shopifyRedirectError=Shopify app authorization redirect exception.
zsmall.thirdParty.shopifyFulfillmentNotExist=The Shopify fulfillment service has not been created, please re-authorize Shopify.
zsmall.thirdParty.shopifyShopAlreadyConnectOther=This Shopify is already associated with other users and cannot be associated again.
zsmall.thirdParty.shopifyAppDisconnectError=An exception occurred when disconnect Shopify.
zsmall.thirdParty.cannotConnectMultipleShopify=Multiple Shopify stores cannot be connected to the same account, please unlink the previous Shopify
zsmall.thirdParty.amazonShopAlreadyConnectOther=This Amazon is already associated with other users and cannot be associated again.
zsmall.thirdParty.amazonAuthenticationBindingError=Amazon app authorization exception, please contact the administrator.
zsmall.thirdParty.uhpGetWelcomeTipsError=Get the homepage welcome prompt exception.
zsmall.thirdParty.uhpGetStatisticsInfoError=Get homepage statistics exception.
zsmall.thirdParty.uhpGetMdDataInfoError=Obtaining employee homepage data information exception.
zsmall.thirdParty.getUserAccountPageError=Turn the page to get the user account list exception.
zsmall.thirdParty.changeUserAccountStatusError=Changing the user account status exception.
zsmall.thirdParty.exportUserAccountExcelError=Export account information excel exception.
zsmall.thirdParty.getStoreInfoError=Obtaining store information exception.
zsmall.thirdParty.checkStoreInfoError=Check the store information exception.
zsmall.thirdParty.addOrEditStoreInfoError=Adding/editing store information exception.
zsmall.thirdParty.changeStoreStatusTypeError=An exception occurred when changing the store status.
zsmall.thirdParty.storeNotExist=Store information does not exist.
zsmall.thirdParty.hasUnfinishedOrderCantClose=There are unfinished orders, the store cannot be closed.
zsmall.thirdParty.hasUnfinishedOrderCantDelete=There are unfinished orders, the store cannot be deleted.
zsmall.thirdParty.storeNameRepeated=Duplicate store name.
zsmall.thirdParty.storeCustomizeUrlNameRepeated=Duplicate store custom link name.
zsmall.thirdParty.changeStoreExistError=An exception occurred when check if the store exists.
zsmall.thirdParty.getStockLockParamError=An exception occurred when obtaining the lock goods setting.
zsmall.thirdParty.saveStockLockParamError=An exception occurred when saving the lock goods settings.
zsmall.thirdParty.noHasStockLockManagementAuthority=No lock goods setting permission.
zsmall.thirdParty.wayfairConnectError=Wayfair connection failed.
zsmall.thirdParty.bindingChannelAccountError=An exception occurred when binding channel accounts.
zsmall.thirdParty.jumpToChannelError=An exception occurred when redirecting channel links.
zsmall.thirdParty.querySalesChannelStatusError=An exception occurred when querying the sales channel association.
zsmall.thirdParty.querySalesChannelInfoError=An exception occurred when querying sales channel information.
zsmall.thirdParty.disconnectSalesChannelError=Unbind exception.
zsmall.thirdParty.paymentMethodChangeStatusError=An exception occurred when the payment method changes status.
zsmall.thirdParty.createCardError=An exception occurred when creating a credit card.
zsmall.thirdParty.createAccountError=An exception occurred when creating a bank account.
zsmall.thirdParty.getPaymentMethodHomeError=An exception occurred when obtaining the payment method homepage information.
zsmall.thirdParty.getUserCardError=An exception occurred when obtaining the user's credit card.
zsmall.thirdParty.getUserAccountError=An exception occurred when obtaining the user's bank account number.
zsmall.thirdParty.getOrderPendingDetailError=An exception occurred when obtaining the incomplete details page.
zsmall.thirdParty.bankAccountNotExist=Bank account does not exist.
zsmall.thirdParty.bankCreditOrDebitNotExist=Credit/Debit card does not exist.
zsmall.thirdParty.insufficientWalletBalance=Insufficient wallet balance.
zsmall.thirdParty.userBankCardIsExist=The credit or debit card already exists for this account.
zsmall.thirdParty.userBankAccountIsExist=The bank card information already exists for the account.
zsmall.thirdParty.paymentIntentWalletError=An exception occurred when create payment intent.
zsmall.thirdParty.notOneselfAccount=Not my bank account.
zsmall.thirdParty.notOneselfCard=Not my bank card.
zsmall.thirdParty.paymentMethodNotExistOrUnbound=The payment method does not exist or has been unbound.
zsmall.thirdParty.setSettleDayError=Set settlement date exception.
zsmall.thirdParty.checkExistAccountIdError=An exception occurred when judging whether the current account has an account Id.
zsmall.thirdParty.payoutIsNull=Failed to create PAYOUT.
zsmall.thirdParty.getTransferError=An exception occurred when obtaining the Transfer card.
zsmall.thirdParty.getTransferInfoError=An exception occurred when obtaining the information of the currently bound transfer.
zsmall.thirdParty.getTransactionsPageError=An exception occurred when turn the page to query the transaction record list.
zsmall.thirdParty.getTransactionsDetailError=An exception occurred when querying transaction record details.
zsmall.thirdParty.transactionsNotExist=Transaction record does not exist.
zsmall.thirdParty.getTransactionsStatisticsError=An exception occurred when querying transaction record statistics.
zsmall.thirdParty.getTransactionOrdersError=An exception occurred when querying transaction records related orders.
zsmall.thirdParty.getAnalyticsListError=An exception occurred when getting the data analysis list.
zsmall.thirdParty.countryInfoNotExist=Country information does not exist.
zsmall.thirdParty.stateNotExist=Province/State does not exist.
zsmall.thirdParty.cityNotExist=City does not exist
zsmall.menu.ssoMenuQueryError=Menu query exception.
zsmall.menu.storemenuSortnumGetError=An exception occurred when getting the menu after sorting.
zsmall.menu.storemenuParentNotExist=The parent menu does not exist.
zsmall.menu.storemenuNotExist=Menu does not exist.
zsmall.menu.storemenuCreateError=An exception occurred when during menu creation.
zsmall.menu.storemenuGetError=An exception occurred when getting menu information.
zsmall.menu.storemenuStatusUpdateError=An exception occurred when the menu status was updated.
zsmall.role.getStoreRolePageError=An exception occurred when querying the role list.
zsmall.role.saveStoreRoleInfoError=An exception occurred when saving role information.
zsmall.role.changeStoreRoleStatusError=An exception occurred when changing the status of the character.
zsmall.role.getStoreRoleUsersError=An exception occurred when getting the user collection under the role.
zsmall.role.getStoreRoleMenusError=An exception occurred when getting the menu list under the role.
zsmall.role.storeRoleNotExist=Role information does not exist.
zsmall.role.saveStoreRoleUsersError=An exception occurred during role user configuration.
zsmall.role.saveStoreRoleMenusError=An exception occurred during role menu configuration.
zsmall.role.userNotHasRole=No role is configured for the current user.
zsmall.business.getBusinessParameterPageError=An exception occurred when obtaining system service parameter information.
zsmall.business.businessParameterTypeNotExist=Business parameter type does not exist.
zsmall.business.businessParameterNotExist=Business parameter does not exist.
zsmall.business.getBusinessParameterDetailError=An exception occurred when obtaining system service parameter details.
zsmall.business.editBusinessParameterError=An exception occurred when editing system business parameters.
zsmall.business.getBusinessParameterTypeListError=An exception occurred when obtaining the list of system business parameter types.
zsmall.article.getArticleRegulationsPageError=An exception occurred when obtaining articles/regulations.
zsmall.article.saveArticleRegulationsError=An exception occurred when saving the article/regulation.
zsmall.article.getArticleRegulationsDetailError=An exception occurred when getting article/regulation details.
zsmall.article.articleRegulationsNotExist=Article/Regulation does not exist.
zsmall.article.articleRegulationsInfoNotExist=Article/Regulation information does not exist.
zsmall.article.getNewsNoticePageError=An exception occurred when querying the message list.
zsmall.article.publishNewsNoticeError=An exception occurred when posting a message.
zsmall.article.getNewsNoticeDetailError=An exception occurred when getting the message details.
zsmall.article.changeNewsNoticeStatusError=An exception occurred when changing the status of the message.
zsmall.article.readNewsNoticeError=An exception occurred when changing the message reading status.
zsmall.article.newsNoticeNotExist=Message does not exist.
zsmall.article.newsNoticeIsDeleted=Message deleted.
zsmall.article.saveNewsNoticeError=An exception occurred when saving the message.
zsmall.article.publishedNewsNoticeDoNotEdit=The posted message cannot be edited.
zsmall.productCategory.getProductCategoryListError=An exception occurred when querying the product category list.
zsmall.productCategory.changeProductCategoryStatusError=An exception occurred when changing the product classification status.
zsmall.productCategory.productCategoryNotExist=Product category does not exist.
zsmall.productCategory.getProductCategoryDetailError=An exception occurred when getting product classification details.
zsmall.productCategory.saveProductCategoryError=An exception occurred when saving the product category.
zsmall.productCategory.hasChildrenDontChangeLevel=There are sub-categories for this category, and the category level cannot be modified.
zsmall.productCategory.changeProductCategorySortError=An exception occurred when changing the product classification and sorting.
zsmall.productCategory.batchChangeProductCategoryInfoError=An exception occurred when changing product classification information in bulk
zsmall.productCategory.productCategoryAttributesNotExist=Product classification attribute does not exist.
zsmall.productCategory.productCategoryAttributesValueExisted=Product classification attribute value already exists.
zsmall.productCategory.getCategoryAttributesListError=An exception occurred when getting classification attributes.
zsmall.product.productAttributesListCantBeEmpty=Product image cannot be empty.
zsmall.product.getProductPageError=An exception occurred when querying the product list.
zsmall.product.changeProductStatusError=An exception occurred when changing the status of the product.
zsmall.product.getProductDetailError=An exception occurred when getting product details.
zsmall.product.saveProductError=An exception occurred when saving the product.
zsmall.product.changeProductShelfTypeError=An exception occurred when onShelf/offShelf a product
zsmall.product.noHasProductManagementAuthority=No product management authority.
zsmall.product.addProductAttributesValueError=An exception occurred when adding product attribute values.
zsmall.product.productAttributesCantCustomNewValue=Product attributes do not support custom new values.
zsmall.product.productReviewError=An exception occurred when during product review.
zsmall.product.getProductSkuDetailError=An exception occurred when obtaining product sku details.
zsmall.product.saveProductSkuError=An exception occurred when saving product sku information.
zsmall.product.getProductLogisticsListError=An exception occurred when obtaining the merchandise logistics collection.
zsmall.product.saveProductLogisticsError=An exception occurred when saving the merchandise logistics.
zsmall.product.submitProductReviewError=An exception occurred when submitting the product for review.
zsmall.product.getStockLockRulesError=An exception occurred when obtaining the lock goods rules.
zsmall.product.setStockLockRulesError=An exception occurred when setting lock goods rules.
zsmall.product.changeStockLockSwitchError=An exception occurred when opening or closing the lock goods function.
zsmall.product.getEditedProductListError=An exception occurred when querying the merchandise list modified by the merchant.
zsmall.product.syncEditedProductError=An exception occurred when syncing merchandise modified by the merchant.
zsmall.product.changeSkuHideTypeError=Show/hide sku exception.
zsmall.product.productNotAcceptedCannotOnshelf=Products that have not passed the review cannot be put on the shelves.
#zsmall.product.productSkuCodeExisted=Product sku already exists.
zsmall.product.productSkuAndUpcIsNotEmpty=Product SKU cannot be empty.
zsmall.product.productSkuRequiresAtLeastOneSpecification=Product sku requires at least one specification.
zsmall.product.productStoreWarehouseAddressCannotBeEmpty=Product attribution warehouse address is required.
zsmall.product.removeProductFromDropshippingError=An exception occurred when moving the product out of the dropshipping List.
zsmall.product.productImportToStoreError=An exception occurred when the product was import to the sales channel.
zsmall.product.siteProductSkuMarkUpError=An exception occurred when setting the product sku mark up.
zsmall.product.changeProductSkuShelfTypeError=An exception occurred when onShelf/offShelf product sku.
zsmall.product.exportDistributorProductExcelError=An exception occurred when exporting distributor product information.
zsmall.product.channelAccountNotConnected=Sales channel account not connected.
zsmall.product.productIsMissingBrandAttributes=Product is missing the brand attribute.
zsmall.product.getLogisticsMethodListError=An exception occurred when obtaining the collection of fulfillment methods.
zsmall.product.productPriceMustBeGreaterThan0=The product price must be greater than 0.
zsmall.product.productPriceCalculateError=Product price calculation is abnormal or tampered with.
zsmall.product.editProductSkuOwnInventoryError=An exception occurred when modifying the product sku's own inventory.
zsmall.product.nonProductSkuOwnInventoryCannotEdit=Non-product SKU's own inventory cannot be modified.
zsmall.product.getAttributesPageError=An exception occurred when querying the attribute list.
zsmall.product.attributesExisted=Attribute already exists.
zsmall.product.attributesNotExist=Attribute does not exist.
zsmall.product.saveAttributesError=An exception occurred when saving properties.
zsmall.product.getAttributesDetailError=An exception occurred when getting property details.
zsmall.product.changeAttributesStatusError=An exception occurred when changing the attribute state.
zsmall.product.importAttributesError=An exception occurred when importing attributes.
zsmall.product.attributesValueExisted=Property value already exists.
zsmall.product.getBasicAttributesListError=Get basic attribute collection exception.
zsmall.product.getUsageUpcError=An exception occurred when get available UPC.
zsmall.product.usageUpcUploadFileError=An exception occurred when upload the upc excel.
zsmall.product.usageUpcGetFileError=An exception occurred when reading of UPC excel file content.
zsmall.product.usageUpcRecordGetError=An exception occurred when get upc usage record.
zsmall.product.upcExisted=UPC already exists:
zsmall.product.upcAndBrandCannotBeEmptyInLine=upc and brand cannot be empty in line:
zsmall.orders.getOrderPageError=An exception occurred when querying the order list.
zsmall.orders.getOrderDetailError=An exception occurred when querying order details.
zsmall.orders.changeOrderItemStatusError=An exception occurred when changing the refund status of a sub-order.
zsmall.orders.changeOrderRecipientInfoError=An exception occurred when changing the status of a sub-order.
zsmall.orders.orderNotSurplusNum=The order has no remaining quantity and cannot be refunded.
zsmall.orders.exportOrdersError=An exception occurred when exporting the order.
zsmall.orders.ordersIsNotExist=Order does not exist.
zsmall.orders.changeOrderPhoneCodeError=An exception occurred when changing the order mobile phone number and area code.
zsmall.orders.manualFulfillError=Manual fulfillment exception.
zsmall.orderRefund.submitRefundApplyError=An exception occurred when submitting the refund request.
zsmall.orderRefund.applyTypeUnknown=Unknown refund request type.
zsmall.orderRefund.orderStatusNotPaid=The order status is not paid and cannot be refunded.
zsmall.orderRefund.notAccordanceRefundRules=Does not meet the refund rules and cannot be refunded.
zsmall.orderRefund.notValidItemCantRefund=No valid sub-orders can be refunded.
zsmall.orderRefund.surplusLessThanRefund=The number of items remaining in the order is less than the number of items that need to be refunded
zsmall.orderRefund.queryRefundApplyListError=An exception occurred when querying the refund request form.
zsmall.orderRefund.refundApplyHandleError=An exception occurred when refund application processing.
zsmall.orderRefund.refundApplyStatusError=The status of the refund order is not under review.
zsmall.orderRefund.supplementRefundInfoError=An exception occurred when completing the return flow information.
zsmall.orderRefund.orderRefundNotExist=The refund slip does not exist.
zsmall.orderRefund.refundItemNotRefunding=Status is not Refunding, unable to complete the information.
zsmall.orderRefund.getRefundDetailError=An exception occurred when getting the refund order details.
zsmall.orderRefund.nowIdentityCantGetRefundList=The current identity cannot access the refund order list.
zsmall.orderRefund.returnTypeUnknown=Unknown return method.
zsmall.orderRefund.getRefundReasonError=An exception occurred when querying the reason for the refund application.
zsmall.orderRefund.confirmReceiptError=An exception occurred when confirming receipt of the return.
zsmall.orderRefund.nowUserNotSup=The current user is not a supplier account and cannot be operated.
zsmall.orderRefund.orderRefundItemStatusNotMatch=The status of the refund order is incorrect and the receipt cannot be confirmed.
zsmall.orderRefund.refundLogisticsNotExist=The return flow information is not filled in, and the receipt cannot be confirmed.
zsmall.logisticsTemplate.findLogisticsError=An exception occurred when querying the list of logistics templates.
zsmall.logisticsTemplate.findLogisticsConditionError=An exception occurred when querying the list of logistics conditions.
zsmall.logisticsTemplate.findLogisticsConditionInfoError=An exception occurred when querying the logistics condition information.
zsmall.logisticsTemplate.logisticsNotExist=Logistics template does not exist.
zsmall.logisticsTemplate.logisticsConditionNotExist=Logistics template conditions do not exist.
zsmall.logisticsTemplate.logisticsConditionInfoNotExist=Logistics template condition information does not exist.
zsmall.logisticsTemplate.deleteLogisticsError=An exception occurred when deleting the logistics template.
zsmall.logisticsTemplate.deleteLogisticsConditionError=An exception occurred when deleting logistics conditions.
zsmall.logisticsTemplate.deleteLogisticsConditionInfoError=An exception occurred when deleting logistics condition information.
zsmall.logisticsTemplate.updateLogisticsConditionError=An exception occurred when modifying the logistics conditions.
zsmall.logisticsTemplate.isNotMdDontUpdateMdTemplate=Non-employee accounts cannot modify the platform template.
zsmall.logisticsTemplate.pleaseCreateConditionForDelete=Please create a condition before deleting.
zsmall.logisticsTemplate.filfillerNameIsExist=The fulfillment company name already exists.
zsmall.logisticsTemplate.minZoneAndMaxZoneNotZero=The minimum area and the maximum area cannot be 0 at the same time.
zsmall.logisticsTemplate.youHasNotThisLogisticsOrThisLogisticsIsDelete=This logistics does not belong to you or the logistics has been deleted.
zsmall.logisticsTemplate.getThirdPartyLogisticsListError=An exception occurred when obtaining the third-party logistics provider information list.
zsmall.logisticsTemplate.storeWarehouseNotExist=Warehouse does not exist
zsmall.logisticsTemplate.getStoreWarehouseListError=An exception occurred when getting the warehouse list.
zsmall.logisticsTemplate.getStoreWarehouseInfoError=An exception occurred when obtaining warehouse information.
zsmall.logisticsTemplate.getStoreWarehouseAddressListError=An exception occurred when getting the warehouse address list.
zsmall.logisticsTemplate.saveStoreWarehouseAddressError=An exception occurred when saving the warehouse address.
zsmall.logisticsTemplate.changeStoreWarehouseAddressStatusTypeError=An exception occurred when changing the effective state of the warehouse address.
zsmall.logisticsTemplate.storeWarehouseAddressNotExist=The warehouse address does not exist or has been deleted.
zsmall.logisticsTemplate.storeWarehouseAddressLinkProductCantDelete=There are products using the warehouse address and cannot be deleted.
zsmall.payment.getClientSecretAndPublickeyError=An exception occurred when get client secret and public key.
zsmall.payment.getCardListError=An exception occurred when get card list.
zsmall.payment.detachPaymentMethodError=An exception occurred when relieve payment method.
zsmall.payment.createPaymentIntentError=An exception occurred when create payment intent.
zsmall.payment.userIsNotExistStripeAccount=User Stripe account not exist.
zsmall.payment.getUserLinkTokenError=An exception occurred when get user link token.
zsmall.payment.bindingPlaidAchError=Failed to bind plaid ACH.
zsmall.marketplace.getUserRecentlyViewedListError=Obtaining user's browsing product record exception.
zsmall.marketplace.getMarketplaceHomeInfoError=Obtaining Marketplace Homepage Information exception.
zsmall.marketplace.getMarketplaceProductDetailError=Obtaining Marketplace product details exception.
zsmall.marketplace.changeProductCollectedStateError=An exception occurred when changing the product's favorite status.
zsmall.marketplace.getWhatsNewProductPageError=Turn the page to query the list of new products on the Marketplace exception.
zsmall.marketplace.getTopRankingProductPageError=Turn the page to query the merchandise list of the Marketplace sales list exception.
zsmall.shoppingCart.getShoppingCartNumError=Query the shopping cart quantity exception.
zsmall.shoppingCart.getShoppingCartInfoError=Query shopping cart information exception.
zsmall.shoppingCart.deleteShoppingCartInfoError=Deleting the shopping cart item sku information exception.
zsmall.shoppingCart.changeShoppingCartNumError=Change the number of sku items in the shopping cart exception.
zsmall.shoppingCart.shoppingCartInfoNotExist=Shopping cart item sku information does not exist.
zsmall.shoppingCart.shoppingCartProductNumAtLeastOne=The number of items in the shopping cart cannot be less than 1.
zsmall.shoppingCart.addToShoppingCartError=Product sku is added to the shopping cart exception.
zsmall.shoppingCart.addStockNotifyError=Add notification record exception.
zsmall.shoppingCart.submitStockLockInfoError=Submit the lock goods information exception.
zsmall.shoppingCart.getCategoryProductPageError=Turn the page to query category product list exception.
zsmall.shoppingCart.getStorePaymentMethodListError=Get payment method exception.
zsmall.shoppingCart.marketplacePlaceOrderError=Marketplace Order placed exception.
zsmall.shoppingCart.productOffShelf=Product [{0}] has been removed from the shelves.
zsmall.shoppingCart.productSkuStockShortage=Insufficient product sku inventory.

# zsmall marketplace
zsmall.mp.static.content.notfound=No article content found.
zsmall.mp.static.content.error=The article content is obtained abnormally.


# zsmall extend
# zsmall extend shop
zsmall.extend.shop.auth.lost=Loss of authorization information, please re-authorize.
zsmall.extend.shop.shopify.auth.expire=Shopify authorization information has expired, please go to the Shopify store and click on Application to authorize again.
zsmall.extend.shop.shopify.connected=Shopify stores have been connected by other accounts, please go to the Shopify application page to click application to log in to the account.
zsmall.extend.shop.shopify.distributor.only=Your Shopify mailbox has been registered with a non-distributor account, and only the distributor can connect the sales channel.

# zsmall excel
zsmall.excel.distributor=Distributor
zsmall.excel.transactionReceiptNo=Transaction Receipt No
zsmall.excel.applicationTime=Application Time
zsmall.excel.transactionType=Transaction Type
zsmall.excel.transactionMethod=Transaction Method
zsmall.excel.transactionAmount=Transaction Amount
zsmall.excel.transactionFee=Transaction Fee
zsmall.excel.transactionTime=Transaction Time
zsmall.excel.receiptTime=Arrival Time
zsmall.excel.accountId=Account Id
zsmall.excel.accountName=Account Name
zsmall.excel.reviewState=Review State
zsmall.excel.note=Remark
zsmall.excel.orderNo=Order No
zsmall.excel.transactionNo=Transaction No
zsmall.excel.transactionSubType=Transaction Sub Type
zsmall.excel.orderTime=Order Time
zsmall.excel.beforeBalance=Before Balance
zsmall.excel.afterBalance=After Balance
zsmall.excel.transactionNote=Transaction Note
zsmall.excel.transactionState=Transaction State
zsmall.excel.withdrawalRecordNo=Receipt ID
zsmall.excel.withdrawalApplyTime=Receipt Apply DateTime
zsmall.excel.withdrawalAccount=Receipt Account
zsmall.excel.withdrawalMethod=Receipt Type
zsmall.excel.withdrawalAmount=Receipt Amount
zsmall.excel.withdrawalState=Receipt Status
zsmall.excel.payee=Payee
zsmall.excel.payer=Payer
zsmall.excel.receiptAccount=Receipt Account
zsmall.excel.paymentMethod=Payment Method
zsmall.excel.transactionTime1=Transaction Time
zsmall.excel.balance=Blance
zsmall.excel.lastRechargeTime=Last Recharge Time
zsmall.excel.lastPaymentTime=Last Payment Time
zsmall.excel.walletState=Wallet State
zsmall.excel.date=Date
zsmall.excel.from=From
zsmall.excel.to=To
zsmall.excel.role=Role
zsmall.excel.operator=Operator
zsmall.excel.tenantId=TenantId
zsmall.excel.supplier=Supplier
zsmall.excel.productSkuCode=ItemNo
zsmall.excel.activityID=Activity ID
zsmall.excel.activityType=Activity Type
zsmall.excel.startTime=Start Time
zsmall.excel.productPickUpPrice=Product PickUp Price
zsmall.excel.platformPickUpPrice=Activity PickUp Price
zsmall.excel.platformFinalDeliveryFee=Activity Final Delivery Fee
zsmall.excel.activityStorageFee=Activity Storage Fee
zsmall.excel.activityState=Activity State
zsmall.excel.thirdChannelFlag=Third Channel Flag
zsmall.excel.currency=Currency
zsmall.excel.deductionAmount=Deduction Amount
zsmall.excel.operator2=Operator
zsmall.productCategory.productCategoryRepeat=Category name repeat
zsmall.excelImport.notValidRow=No valid data rows found
zsmall.excelImport.productNameLimit=Product name exceeds the limit length(150)
zsmall.excelImport.invalidContent=The content filled in is invalid
zsmall.excelImport.productCategoryNotFound=Product category not found
zsmall.excelImport.productVariantDimensionNotFound=The product variant dimension was not found or does not belong to the current category
zsmall.excelImport.productVariantNotSupportCustom=The filled variant dimension does not support custom values
zsmall.excelImport.productVariantValueRepeat=Duplicate variant values
zsmall.excelImport.duplicateVariant=Duplicate variant dimensions
zsmall.excelImport.duplicateVariantValueCombination=Duplicate variant combinations
zsmall.excelImport.warehouseNotFound=Warehouse not found
zsmall.excelImport.logisticsTemplateNotFound=Logistics template not found or not associated with warehouse
zsmall.excelImport.warehouseTypeMustBeSame=The types of warehouses must be the same
zsmall.excelImport.invalidImage=Invalid image
zsmall.excelImport.priceCannotBeLessThanZero=Price cannot be less than zero
zsmall.systemCommon.nowUserNoPermission=
zsmall.systemCommon.sheetNotNull=
zsmall.systemCommon.requiedIsNotEmpty=
zsmall.excelImport.unknownParsingError=Unknown parsing error
zsmall.excelImport.skuRepeat=SKU repeat!
zsmall.excelImport.upcRepeat=UPC repeat!
zsmall.product.productExistsRequiredSpec=The required specifications are blank. Please complete before submitting for review
zsmall.excelImport.productSkuNotInWarehouse=SKU does not exist in the specified warehouse
zsmall.productDropShipping.costPriceHasChanged=The cost price has changed. Please refresh the page and try again.
zsmall.excelImport.variantValueDuplicate=Duplicate variant value combination
zsmall.global.saveImportDataError=Unknown error while saving imported data
zsmall.global.paymentFailed=Payment failed. Please try again later.
zsmall.productActivity.adjustBulkActivityError=Unknown error occurred when adjust distributor's activity.
zsmall.productActivity.adjustInventoryCannotBeLessThan=The Warehouse [{0}] stock adjustment after ({1}) not less than sold inventory ({2}).
zsmall.productActivity.adjustInventoryCannotBeLessThanZero=The Warehouse [{0}] surplus stock adjustment after not less than 0.
zsmall.productActivity.inventoryUnchanged=Stock unchanged.
zsmall.productActivity.adjustingInventoryNotSupported=Adjusting stock for this activity is not supported.
zsmall.productActivity.queryBulkActivityInventoryError=Unknown error occurred when query distributor's activity stock.
zsmall.productActivity.activityNotAvailable=Activity[{0}] not available.
zsmall.product.noAvailableStockFound=SkuId.[{0}] no available stock found,or out of stock.

#\u5546\u54C1\u7EDF\u8BA1\u5BFC\u51FA\u5B57\u6BB5
zsmall.excel.statistics.manager.itemNo=Item No
zsmall.excel.statistics.manager.name=Product Name
zsmall.excel.statistics.manager.showUrl=Product Image Url
zsmall.excel.statistics.manager.sku=SKU
zsmall.excel.statistics.manager.tenantId=Supplier ID
zsmall.excel.statistics.manager.createTime=Registration Time
zsmall.excel.statistics.manager.updateTime=Update Time
zsmall.excel.statistics.manager.stockTotal=Stock Total
zsmall.excel.statistics.manager.dropShippingPrice=Drop Shipping Price
zsmall.excel.statistics.manager.pickUpPrice=Pick Up Price
zsmall.excel.statistics.manager.salesNum=Sales Num
zsmall.excel.statistics.manager.dropNumByDis=Total Collections (Distributors)
zsmall.excel.statistics.manager.orderDealEffectiveness=Processing Order Timeliness
zsmall.excel.statistics.manager.downloadedByDis=Downloads
zsmall.excel.statistics.manager.orderTotalNum=Total Orders
zsmall.excel.statistics.manager.orderTotalPrice=Total Order Amount
zsmall.excel.statistics.manager.restockTotalNum=Total After Sales Orders
zsmall.excel.statistics.distributor.tenantId=Distributor ID
zsmall.excel.statistics.distributor.companyName=Company Name
zsmall.excel.statistics.distributor.email=Email
zsmall.excel.statistics.distributor.phoneNumber=Phone Number
zsmall.excel.statistics.distributor.createTime=Registration Time
zsmall.excel.statistics.distributor.mainProducts=Main Products
zsmall.excel.statistics.distributor.skuDistributionNum=SKU Distribution Num
zsmall.excel.statistics.distributor.skuOrderNum=SKU Orders Issued
zsmall.excel.statistics.distributor.salesNum=Sales Num
zsmall.excel.statistics.distributor.orderTotalNum=Total Orders
zsmall.excel.statistics.distributor.orderTotalPrice=Total Order Amount
zsmall.excel.statistics.distributor.restockTotalNum=Total After Sales Orders
zsmall.excel.statistics.distributor.downloadedByDis=Downloads
zsmall.excel.statistics.distributor.dropNumByDis=Drop Num
zsmall.excel.statistics.distributor.clickedSku=Clicked Sku
zsmall.excel.statistics.supplier.skuNum=SKU Num
zsmall.excel.statistics.supplier.tenantId=Supplier ID
zsmall.excel.statistics.supplier.companyName=Company Name
zsmall.excel.statistics.supplier.email=Email
zsmall.excel.statistics.supplier.phoneNumber=Phone Number
zsmall.excel.statistics.supplier.createTime=Registration Time
zsmall.excel.statistics.supplier.mainProducts=Main Products
zsmall.excel.statistics.supplier.skuInventoryNum=Stock SKU Num
zsmall.excel.statistics.supplier.skuOrderNum=SKUs with orders issued
zsmall.excel.statistics.supplier.salesNum=Sales Num
zsmall.excel.statistics.supplier.orderTotalNum=Total Orders
zsmall.excel.statistics.supplier.orderTotalPrice=Total Orders Price
zsmall.excel.statistics.supplier.dropNumByDis=Collections (Distributors Num)
zsmall.excel.statistics.supplier.orderDealEffectiveness=Order Processing Time Limit (hours)

#\u5546\u54C1\u7EDF\u8BA1\u5BFC\u51FA\u5B57\u6BB5
zsmall.excel.chinaSpotProduct.tenantId=Supplier Tenant ID
zsmall.excel.chinaSpotProduct.productCode=Product Code
zsmall.excel.chinaSpotProduct.categoryName=Category Name
zsmall.excel.chinaSpotProduct.productName=Product Name
zsmall.excel.chinaSpotProduct.minimumQuantity=Minimum Quantity
zsmall.excel.chinaSpotProduct.sku=SKU
zsmall.excel.chinaSpotProduct.fixedSpecValue=Fixed Spec Value
zsmall.excel.chinaSpotProduct.warehouseBelongCity=Warehouse Belong City
zsmall.excel.chinaSpotProduct.quantity=Quantity
zsmall.excel.chinaSpotProduct.length=Length
zsmall.excel.chinaSpotProduct.width=Width
zsmall.excel.chinaSpotProduct.height=Height
zsmall.excel.chinaSpotProduct.weight=Weight
zsmall.excel.chinaSpotProduct.packLength=Pack Length
zsmall.excel.chinaSpotProduct.packWidth=Pack Width
zsmall.excel.chinaSpotProduct.packHeight=Pack Height
zsmall.excel.chinaSpotProduct.packWeight=Pack Weight
zsmall.excel.chinaSpotProduct.referencePrice=Reference Price
zsmall.excel.chinaSpotProduct.image1=Image1
zsmall.excel.chinaSpotProduct.image2=Image2
zsmall.excel.chinaSpotProduct.image3=Image3
zsmall.excel.chinaSpotProduct.image4=Image4
zsmall.excel.chinaSpotProduct.image5=Image5
zsmall.excel.chinaSpotProduct.description=Description
zsmall.productActivity.deductionAmountCannotBeGreaterThan=The deduction amount cannot be greater than the supplier total amount ${0}.
zsmall.productActivity.activityCancelSuccess=Activity cancelled successfully!
zsmall.productActivity.activityCancelRequestSubmitted=The activity cancellation application has been submitted, please wait for approval
zsmall.warehouseManagement.warehouseIsAlreadyInUse=The warehouse is already in use by products or promotional activities and cannot be deleted
zsmall.product.productIsNotStoredInTheWarehouse=The product is not stored in the warehouse [{0}]
zsmall.excel.productName=Product Name
zsmall.excel.pickUpPrice=Pick Up Price
zsmall.excel.dropShippingPrice=DropShipping Price
zsmall.excel.stockTotal=Stock Quantity
zsmall.excel.productCode=Product Code
zsmall.excel.imageShowUrl=Product Image
zsmall.excel.productCategory=Product Category
zsmall.excel.features=Features
zsmall.excel.description=Product Description
zsmall.excel.optionalSpec=Optional Specifications
zsmall.excel.supportedLogistics=Supported Logistics
zsmall.excel.shippingReturnDescription=Shipping and Return Policy
zsmall.excel.productLink=Product Link
zsmall.excel.specComposeName=Specification Combination
zsmall.excel.length=Length
zsmall.excel.width=Width
zsmall.excel.height=Height
zsmall.excel.weight=Weight
zsmall.excel.packLength=Packing Length
zsmall.excel.packWidth=Packing Width
zsmall.excel.packHeight=Packing Height
zsmall.excel.packWeight=Packing Weight
zsmall.excel.msrp=MSRP
zsmall.excel.warehouseSystemCode=Warehouse ID
zsmall.excel.genericSpec=Generic Specification
zsmall.product.itemNoNotOnShelf=ItemNo.{0} Not for sale.
zsmall.excel.nickName=Nick Name
zsmall.excel.tenantType=Tenant Type
zsmall.excel.email=Email Address
zsmall.excel.phoneNumber=Phone Number
zsmall.excel.createTime=Creation Time
zsmall.excel.loginDate=Last Login Time
zsmall.excel.tenantStatus=Tenant Status
zsmall.excelFormat.tenantType.Manager=Manager
zsmall.excelFormat.tenantType.Supplier=Supplier
zsmall.excelFormat.tenantType.Distributor=Distributor
zsmall.excelFormat.tenantStatus.0=Enable
zsmall.excelFormat.tenantStatus.1=Disable
zsmall.orderAfterSales.unmodifiableAmountReason=Please select a refund reason for an unmodifiable amount.
zsmall.orders.batchUploadShippingLabelTips=<p>Batch upload label results:</p>
zsmall.orders.shippingLabelMatch=Matched
zsmall.orders.shippingLabelNotMatch=Unmatched
zsmall.orders.shippingLabelSkuNotMatch=The shippinglabel does not match the number of SKUs.
zsmall.productDropShipping.pleaseSelectRakutenGenreFirst=Please select the Rakuten Genre in 'Edit Product' before synchronizing the product
zsmall.thirdPartyPlatforms.rakutenImageUploadFailed=Rakuten image upload failed!
zsmall.productDropShipping.productLogisticsMethodNotApplicable=The logistics method for product [{0}] is not applicable to {1}.
zsmall.product.delist=The product has been removed from the shelves
zsmall.product.supportedLogisticsIsNotNull=The supported delivery mode cannot be empty
zsmall.product.errorSupportedLogistics=Wrong logistics mode, only support self-pickup/generation
zsmall.product.commodityQuantityOvershoot=The quantity of goods exceeds the limit
zsmall.finalDeliveryFee.productNotFoundError=Product not found
zsmall.finalDeliveryFee.unsupportedLogisticsMethods=Unsupported logistics methods[{0}]

zsmall.transaction.transactionSubTypeNotNull=Transaction subtype cannot be empty

zsmall.warehouse.fieldCannotEmpty={0} Field cannot be empty;
zsmall.warehouse.fieldRepeat={0} Field cannot be repeated;
zsmall.warehouse.fieldLengthMax={0} Field length cannot exceed {1} characters;
zsmall.warehouse.fieldNotExist={0} Field does not exist;
zsmall.warehouse.fieldFormatError={0} Field format error;

zsmall.storage.fee.state.error=Storage fee state error, please check,storageFeeId: {0}
zsmall.storage.fee.not.exist=Storage fee does not exist, please check,storageFeeId: {0}
# Product Active Import Error Messages
zsmall.productActiveImport.fieldCannotEmpty={0},\u003b
zsmall.productActiveImport.fieldRepeat={0},\u003b
zsmall.productActiveImport.fieldLengthMax={0},\u003b
zsmall.productActiveImport.fieldNotExist={0},\u003b
zsmall.productActiveImport.objectNotExist={0},\u003b
zsmall.productActiveImport.fieldFormatError={0},\u003b
zsmall.productActiveImport.fieldValueInconsistent={0},\u003b
zsmall.productActiveImport.warehouseStockInconsistent={0},\u003b

