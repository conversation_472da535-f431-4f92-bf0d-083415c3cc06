package com.hengjian.stream.mq.config;

import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.stream.mq.utils.AliyunCredentialsProvider;
import com.hengjian.stream.mq.utils.AmqpProducer;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2023/12/13 11:37
 */
@Configuration

public class RabbitMqConfig {

    @Value("${amqp.provider}")
    private String amqpProvider;

    @Value("${aliyun.resOwnerId}")
    private long resOwnerId;

    @Value("${spring.rabbitmq.addresses}")
    private String addresses;

    @Value("${spring.rabbitmq.username}")
    private String username;

    @Value("${spring.rabbitmq.password}")
    private String password;

    @Value("${spring.rabbitmq.virtualhost}")
    private String virtualhost;

    @Autowired
    private RabbitProperties rabbitProperties;
    @Bean
    public ConnectionFactory connectionFactory() {
        com.rabbitmq.client.ConnectionFactory rabbitConnectionFactory =
            new com.rabbitmq.client.ConnectionFactory();
        rabbitConnectionFactory.setVirtualHost(rabbitProperties.getVirtualHost());

        if (amqpProvider.equals("aliyun")) {
            AliyunCredentialsProvider credentialsProvider = new AliyunCredentialsProvider(
                rabbitProperties.getUsername(), rabbitProperties.getPassword(), resOwnerId);
            rabbitConnectionFactory.setCredentialsProvider(credentialsProvider);
        } else {
            rabbitConnectionFactory.setUsername(rabbitProperties.getUsername());
            rabbitConnectionFactory.setPassword(rabbitProperties.getPassword());
        }

        rabbitConnectionFactory.setAutomaticRecoveryEnabled(true);
        rabbitConnectionFactory.setNetworkRecoveryInterval(5000);

        ConnectionFactory connectionFactory = new CachingConnectionFactory(rabbitConnectionFactory);
//        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        ((CachingConnectionFactory) connectionFactory).setPublisherConfirms(true);
        ((CachingConnectionFactory) connectionFactory).setPublisherReturns(rabbitProperties.isPublisherReturns());

        ((CachingConnectionFactory) connectionFactory).setAddresses(rabbitProperties.getAddresses());
        ((CachingConnectionFactory) connectionFactory).setUsername(rabbitProperties.getUsername());
        ((CachingConnectionFactory) connectionFactory).setPassword(rabbitProperties.getPassword());
        ((CachingConnectionFactory) connectionFactory).setVirtualHost(rabbitProperties.getVirtualHost());
        ((CachingConnectionFactory) connectionFactory).setShuffleAddresses(true);
        return connectionFactory;
    }

    /**
     * 创建一个管理器（org.springframework.amqp.rabbit.core.RabbitAdmin），用于管理交换，队列和绑定。
     * auto-startup 指定是否自动声明上下文中的队列,交换和绑定, 默认值为true。
     */
    @Bean
    public RabbitAdmin rabbitAdmin(ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        rabbitAdmin.setAutoStartup(true);
        return rabbitAdmin;
    }


    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setMessageConverter(new org.springframework.amqp.support.converter.SimpleMessageConverter());
        return rabbitTemplate;
    }

    @Bean
    public MessageConverter messageConverter() {
        return null;
//        return new Jackson2JsonMessageConverter();
    }

    @Bean
    public AmqpProducer amqpProducer(ConnectionFactory connectionFactory) {
        return new AmqpProducer(connectionFactory);
    }

    @Bean
    public Queue storeSyncQueue() {
        // 队列持久
        return new Queue(RabbitMqConstant.STORE_SYNCHRONIZATION_QUEUE, true);

    }
    @Bean
    public Binding storeSyncQueueQueueBinding() {
        return BindingBuilder.bind(storeSyncQueue()).to(storeSyncQueueExchange()).with(RabbitMqConstant.STORE_SYNCHRONIZATION_KEY);
    }
    // 定义普通Exchange
    @Bean
    public DirectExchange storeSyncQueueExchange() {
        return new DirectExchange(RabbitMqConstant.STORE_SYNCHRONIZATION_EXCHANGE);
    }

    @Bean
    public Queue shopSyncMultiQueue() {
        // 队列持久
        return new Queue(RabbitMqConstant.SHOP_SYNC_MULTI_QUEUE, true,false,false,null);

    }
    @Bean
    public Binding shopSyncMultiQueueBinding() {
        return BindingBuilder.bind(shopSyncMultiQueue()).to(shopSyncMultiQueueExchange()).with(RabbitMqConstant.SHOP_SYNC_MULTI_KEY);
    }
    // 定义普通Exchange
    @Bean
    public DirectExchange shopSyncMultiQueueExchange() {
        return new DirectExchange(RabbitMqConstant.SHOP_SYNC_MULTI_EXCHANGE);
    }

    // 定义多渠道Exchange
    @Bean
    public DirectExchange multichannelSendExchange() {
        return new DirectExchange(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE);
    }

    /**
     * 店铺信息回传队列
     *
     * @return
     */
    @Bean
    public Queue tiktokShopQueue() {
        return new Queue(RabbitMqConstant.TIKTOK_SHOP_QUEUE, true,false,false,null);
    }

    /**
     * 店铺信息回传绑定
     *
     * @return
     */
    @Bean
    public Binding tiktokShopQueueBinding() {
        return BindingBuilder.bind(tiktokShopQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.TIKTOK_SHOP_ROUTING_KEY);
    }


    /**
     * 订单Tracking信息回传队列
     *
     * @return
     */
    @Bean
    public Queue orderTrackingQueue() {
        return new Queue(RabbitMqConstant.ORDER_TRACKING_QUEUE, true,false,false,null);
    }

    /**
     * 订单Tracking信息回传绑定
     *
     * @return
     */
    @Bean
    public Binding orderTrackingQueueBinding() {
        return BindingBuilder.bind(orderTrackingQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.ORDER_TRACKING_ROUTING_KEY);
    }

    // temu订单 交换机
    @Bean
    public DirectExchange temuOrderInfoExchange() {
        return new DirectExchange(RabbitMqConstant.TEMU_ORDER_INFO_EXCHANGE);
    }
    /**
     * 商品映射请求队列
     * @return
     */
    @Bean
    public Queue productMappingRequestQueue() {
        return new Queue(RabbitMqConstant.PRODUCT_MAPPING_REQUEST, true,false,false,null);
    }

    /**
     * 商品映射响应队列
     * @return
     */
    @Bean
    public Queue productMappingResponseQueue() {
        return new Queue(RabbitMqConstant.PRODUCT_MAPPING_RESPONSE, true,false,false,null);
    }

    @Bean
    public Binding productMappingRequestBinding() {
        return BindingBuilder.bind(productMappingRequestQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.PRODUCT_MAPPING_REQUEST);
    }

    @Bean
    public Binding productMappingResponseBinding() {
        return BindingBuilder.bind(productMappingResponseQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.PRODUCT_MAPPING_RESPONSE);
    }

    /**
     * 店铺信息回传队列
     *
     * @return
     */
    @Bean
    public Queue temuOrderInfoQueue() {
        return new Queue(RabbitMqConstant.TEMU_ORDER_INFO_QUEUE, true,false,false,null);
    }

    /**
     * temu信息回传绑定
     *
     * @return
     */
    @Bean
    public Binding temuOrderInfoQueueBinding() {
        return BindingBuilder.bind(temuOrderInfoQueue()).to(temuOrderInfoExchange()).with(RabbitMqConstant.TEMU_ORDER_INFO_ROUTING_KEY);
    }

    /**
     * amazonVC订单物流信息上传队列
     * @return
     */
    @Bean
    public Queue orderAmazonVcLogisticsAttachmentQueue() {
        return new Queue(RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_QUEUE, true,false,false,null);
    }

    /**
     * amazonVC订单物流信息上传绑定
     * @return
     */
    @Bean
    public Binding orderAmazonVcLogisticsAttachmentBinding() {
        return BindingBuilder.bind(orderAmazonVcLogisticsAttachmentQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_ROUTING_KEY);
    }

    /**
     * amazonVC订单物流信息回调队列
     * @return
     */
    @Bean
    public Queue orderAmazonVcLogisticsAttachmentCallBackQueue() {
        return new Queue(RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_CALL_BACK_QUEUE, true,false,false,null);
    }

    /**
     * amazonVC订单物流信息回调绑定
     * @return
     */
    @Bean
    public Binding orderAmazonVcLogisticsAttachmentCallBackBinding() {
        return BindingBuilder.bind(orderAmazonVcLogisticsAttachmentCallBackQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.ORDER_AMAZON_VC_LOGISTICS_ATTACHMENT_CALL_ROUTING_KEY);
    }
    /**
     * 功能描述：open-api 支付 交换
     *
     * @return {@link DirectExchange }
     * <AUTHOR>
     * @date 2024/09/14
     */
    @Bean
    public DirectExchange openPayExchange() {
        return new DirectExchange(RabbitMqConstant.OPEN_PAY_EXCHANGE);
    }
    /**
     * open-api 支付队列
     *
     * @return
     */
    @Bean
    public Queue openPayQueue() {
        return new Queue(RabbitMqConstant.OPEN_PAY_QUEUE, true,false,false,null);
    }

    /**
     * open-api 支付绑定
     *
     * @return
     */
    @Bean
    public Binding openPayBinding() {
        return BindingBuilder.bind(openPayQueue()).to(openPayExchange()).with(RabbitMqConstant.OPEN_PAY_KEY);
    }


    /**
     * 功能描述：分销订单交换机
     * @return
     */
    @Bean
    public DirectExchange distributionOrderExchange() {
        return new DirectExchange(RabbitMqConstant.DISTRIBUTION_ORDER_EXCHANGE);
    }

    /**
     * 功能描述：分销推送订单队列
     * @return
     */
    @Bean
    public Queue distributionPushOrderQueue() {
        return new Queue(RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_QUEUE, true,false,false,null);
    }

    /**
     * 功能描述：分销推送订单绑定
     * @return
     */
    @Bean
    public Binding distributionPushOrderBinding() {
        return BindingBuilder.bind(distributionPushOrderQueue()).to(distributionOrderExchange()).with(RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_ROUTING_KEY);
    }

    /**
     * 功能描述：分销推送订单队列
     * @return
     */
    @Bean
    public Queue distributionOrderPayQueue() {
        return new Queue(RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_PAY_QUEUE, true,false,false,null);
    }

    /**
     * 功能描述：分销推送订单绑定
     * @return
     */
    @Bean
    public Binding distributionOrderPayBinding() {
        return BindingBuilder.bind(distributionOrderPayQueue()).to(distributionOrderExchange()).with(RabbitMqConstant.DISTRIBUTION_PUSH_ORDER_PAY_ROUTING_KEY);
    }

    /**
     * bill确认推送ERP请求队列
     * @return
     */
    @Bean
    public Queue billConfirmedSendErpRequestQueue() {
        return new Queue(RabbitMqConstant.BILL_CONFIRMED_SEND_ERP_REQUEST, true,false,false,null);
    }
    /**
     * 功能描述：bill确认推送ERP绑定
     * @return
     */
    @Bean
    public Binding billConfirmedSendErpRequestBinding() {
        return BindingBuilder.bind(billConfirmedSendErpRequestQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.BILL_CONFIRMED_SEND_ERP_REQUEST);
    }
    /**
     * 账单修补差值推送ERP队列
     * @return
     */
    @Bean
    public Queue billRepairQueue() {
        return new Queue(RabbitMqConstant.BILL_REPAIR_SEND_ERP_REQUEST_QUEUE, true,false,false,null);
    }
    /**
     * 账单修补差值推送ERP绑定
     * @return
     */
    @Bean
    public Binding billRepairBinding() {
        return BindingBuilder.bind(billRepairQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.BILL_REPAIR_SEND_ERP_REQUEST_QUEUE);
    }
    /**
     * 订单tracking上传失败队列
     * @return
     */
    @Bean Queue trackingNotifyErrorQueue(){
        return new Queue(RabbitMqConstant.TRACKING_NOTIFY_ERROR_QUEUE,true,false,false,null);
    }

    /**
     * 订单tracking上传失败绑定
     * @return
     */
    @Bean Binding trackingNotifyErrorQueueBinding(){
        return BindingBuilder.bind(trackingNotifyErrorQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.TRACKING_NOTIFY_ERROR_QUEUE);
    }

    @Bean(name = "canalListenerContainerFactory")
    public SimpleRabbitListenerContainerFactory customContainerFactory(ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        factory.setPrefetchCount(1);
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }

    /**
     * canal订单 交换机
     * @return DirectExchange
     */
    @Bean
    public DirectExchange canalOrderExchange() {
        return new DirectExchange(RabbitMqConstant.CANAL_ORDER_EXCHANGE);
    }

    /**
     * canal订单 队列
     * @return Queue
     */
    @Bean
    public Queue canalOrderQueue( RabbitAdmin rabbitAdmin) {
        Map<String, Object> arguments = new HashMap<>();
        // 设置单一活动消费者模式
        arguments.put("x-single-active-consumer", true);
        Queue queue = new Queue(RabbitMqConstant.CANAL_ORDER_QUEUE, true, false, false, arguments);
        queue.setAdminsThatShouldDeclare(rabbitAdmin);
        return queue;
    }

    /**
     * canal订单 交换机/队列绑定
     * @return Queue
     */
    @Bean
    public Binding canalOrderQueueBinding(RabbitAdmin rabbitAdmin){
        return BindingBuilder.bind(canalOrderQueue(rabbitAdmin)).to(canalOrderExchange()).with(RabbitMqConstant.CANAL_KEY);
    }

    /**
     * 分销商品ES同步队列
     * @return
     */
    @Bean Queue esProductSynchronizationQueue(){
        return new Queue(RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE,true,false,false,null);
    }

    /**
     * 分销商品ES同步队列绑定
     * @return
     */
    @Bean Binding esProductSynchronizationQueueBinding(){
        return BindingBuilder.bind(esProductSynchronizationQueue()).to(multichannelSendExchange()).with(RabbitMqConstant.ES_PRODUCT_SYNCHRONIZATION_QUEUE);
    }

    // ==================== 分销商活动过期延时队列配置（使用延时插件） ====================

    /**
     * 分销商活动过期延时交换机
     * 使用 x-delayed-message 类型，需要安装 rabbitmq-delayed-message-exchange 插件
     */
    @Bean
    public CustomExchange distributorActivityExpireDelayedExchange() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-delayed-type", "direct");
        return new CustomExchange(RabbitMqConstant.ACTIVITY_EXPIRE_DELAYED_EXCHANGE,
                                "x-delayed-message", true, false, arguments);
    }

    /**
     * 分销商活动过期处理队列（直接消费队列）
     */
    @Bean
    public Queue distributorActivityExpireProcessQueue() {
        return new Queue(RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE, true, false, false, null);
    }

    /**
     * 延时队列绑定
     */
    @Bean
    public Binding distributorActivityExpireDelayedBinding() {
        return BindingBuilder.bind(distributorActivityExpireProcessQueue())
                .to(distributorActivityExpireDelayedExchange())
                .with(RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY)
                .noargs();
    }

    // ==================== 兼容旧版TTL方案（可选保留） ====================

    /**
     * 分销商活动过期普通交换机（兼容旧版）
     */
    @Bean
    public DirectExchange distributorActivityExpireExchange() {
        return new DirectExchange(RabbitMqConstant.ACTIVITY_EXPIRE_EXCHANGE, true, false);
    }

    /**
     * 分销商活动过期死信交换机（兼容旧版）
     */
    @Bean
    public DirectExchange distributorActivityExpireDlxExchange() {
        return new DirectExchange(RabbitMqConstant.ACTIVITY_EXPIRE_DLX_EXCHANGE, true, false);
    }

    /**
     * 分销商活动过期TTL队列（兼容旧版）
     * 消息在此队列中等待TTL过期，过期后转发到死信队列
     */
    @Bean
    public Queue distributorActivityExpireTtlQueue() {
        Map<String, Object> arguments = new HashMap<>();
        // 设置死信交换机
        arguments.put("x-dead-letter-exchange", RabbitMqConstant.ACTIVITY_EXPIRE_DLX_EXCHANGE);
        // 设置死信路由键
        arguments.put("x-dead-letter-routing-key", RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY);

        return new Queue(RabbitMqConstant.ACTIVITY_EXPIRE_TTL_QUEUE, true, false, false, arguments);
    }

    /**
     * TTL队列绑定（兼容旧版）
     */
    @Bean
    public Binding distributorActivityExpireTtlBinding() {
        return BindingBuilder.bind(distributorActivityExpireTtlQueue())
                .to(distributorActivityExpireExchange())
                .with(RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY);
    }

    /**
     * 处理队列绑定（兼容旧版）
     */
    @Bean
    public Binding distributorActivityExpireProcessBinding() {
        return BindingBuilder.bind(distributorActivityExpireProcessQueue())
                .to(distributorActivityExpireDlxExchange())
                .with(RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY);
    }



    @Bean
    public Queue reviewCompanyQueue() {
        // 队列持久
        return new Queue(RabbitMqConstant.REVIEW_COMPANY_INFORMATION_QUEUE, true);

    }
    @Bean
    public Binding reviewCompanyBinding() {
        return BindingBuilder.bind(reviewCompanyQueue()).to(reviewCompanyQueueExchange()).with(RabbitMqConstant.REVIEW_COMPANY_INFORMATION_KEY);
    }
    // 定义普通Exchange
    @Bean
    public DirectExchange reviewCompanyQueueExchange() {
        return new DirectExchange(RabbitMqConstant.REVIEW_COMPANY_INFORMATION_EXCHANGE);
    }
}
