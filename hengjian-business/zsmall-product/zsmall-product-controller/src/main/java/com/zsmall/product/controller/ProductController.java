package com.zsmall.product.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.RStatusCodeException;
import com.hengjian.common.idempotent.annotation.RepeatSubmit;
import com.hengjian.common.log.annotation.Log;
import com.hengjian.common.log.enums.BusinessType;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.web.core.BaseController;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.service.impl.SysTenantServiceImpl;
import com.zsmall.common.enums.statuscode.ZSMallStatusCodeEnum;
import com.zsmall.product.biz.service.ProductService;
import com.zsmall.product.entity.domain.bo.product.*;
import com.zsmall.product.entity.domain.vo.ProductRelatedDataCopyVo;
import com.zsmall.product.entity.domain.vo.product.*;
import com.zsmall.product.entity.domain.vo.productImport.ProductImportRecordVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商品SPU
 *
 * <AUTHOR> Li
 * @date 2023-05-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/business/product")
public class ProductController extends BaseController {


    private final SysTenantServiceImpl sysTenantService;
    private final ProductService productService;

    /**
     * 查询商品SPU列表
     */
    @GetMapping("/list")
    public ProductTableInfoVo list(ProductQueryBo bo, PageQuery pageQuery) {
        return productService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询商品完整信息
     */
    @PostMapping("/queryDetail")
    public R<ProductIntactInfoVo> queryDetail(@RequestBody ProductBo bo) throws RStatusCodeException {
        return R.ok(productService.queryIntactInfo(bo));
    }

    /**
     *
     * 导出商品列表
     */
    @GetMapping(value = "/export")
    public R<Void> export(ProductQueryBo bo, PageQuery pageQuery, HttpServletResponse response) throws RStatusCodeException {
//        productService.exportAsync(bo, pageQuery, response);
//        return R.ok();
        String tenantType = LoginHelper.getTenantType();
        if (StrUtil.isBlank(tenantType)) {
            return R.fail("请先登录");
        }
        if (ObjectUtil.notEqual(LoginHelper.getTenantType(), TenantType.Supplier.name())){
            return R.fail("商品导出只支持供应商");
        }
        bo.setTenantId(LoginHelper.getTenantId());
        productService.productListExportNew(bo,response);
        return R.ok();
    }

    /**
     * 新增商品（完整信息）
     */
    @Log(title = "新增商品", businessType = BusinessType.INSERT)
//    @SaCheckPermission("product:add")
    @RepeatSubmit()
    @PostMapping()
    public R<ProductSaveVo> add(@RequestBody ProductIntactInfoBo bo) throws RStatusCodeException {
        R<ProductSaveVo> r = R.ok(productService.insertByIntactInfo(bo));

        return r;
    }

    /**
     * 修改商品（完整信息）
     */
    @Log(title = "修改商品", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<ProductSaveVo> edit(@RequestBody ProductIntactInfoUpdateBo bo) throws RStatusCodeException {
        ProductSaveVo productSaveVo = productService.updateByIntactInfo(bo);
        if (productSaveVo.isHasPriceChange()) {
            return R.ok(ZSMallStatusCodeEnum.HAS_PRICE_CHANGE.getMessage(), productSaveVo);
        } else {
            return R.ok(productSaveVo);
        }
    }

    /**
     * 商品上架/下架
     */
    @Log(title = "商品上架/下架", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/onOrOffShelfProduct")
    public R<Void> onOrOffShelfProduct(@RequestBody ProductShelfBo bo) throws RStatusCodeException {
        return productService.onOrOffShelfProduct(bo);
    }

    /**
     * 删除商品
     */
    @Log(title = "删除商品", businessType = BusinessType.DELETE)
    @RepeatSubmit()
    @DeleteMapping()
    public R<Void> delete(@RequestBody ProductDeleteBo bo) throws Exception {
        if (productService.deleteProduct(bo)) {
            return R.ok();
        } else {
            return R.fail();
        }
    }

    /**
     * 翻页查询商品SKU列表
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/getProductSkuPage")
    public TableDataInfo<ProductSkuSimpleVo> getProductSkuPage(ProductPriceBo bo, PageQuery pageQuery) {
        return productService.getProductSkuPage(bo, pageQuery);
    }

    /**
     * 翻页查询商品价格修改列表
     * @param bo
     * @param pageQuery
     * @return
     */
    @GetMapping("/getProductPriceChange")
    public TableDataInfo<ProductPriceVo> getProductPriceChange(ProductPriceBo bo, PageQuery pageQuery) {
        return productService.getProductPriceChange(bo, pageQuery);
    }

    /**
     * 修改价格
     * todo 需要加锁
     * @param bo
     * @return
     */
    @PostMapping("/changeProductPrice")
    public R<Void> changeProductPrice(@RequestBody PriceFormListBySupBo bo) throws RStatusCodeException {
        return productService.changeProductPrice(bo);
    }

    /**
     * 分页查询商品导入记录
     * @param pageQuery
     * @return
     */
    @GetMapping("/queryProductImportRecordPage")
    public TableDataInfo<ProductImportRecordVo> queryProductImportRecordPage(PageQuery pageQuery) {
        return productService.queryProductImportRecordPage(pageQuery);
    }

    /**
     * 上传商品Excel
     */
    @PostMapping(value = "/uploadProductExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> uploadProductExcel(@RequestPart("file") MultipartFile file) throws Exception {
        return productService.uploadProductExcelNotAsync(file);
    }

    /**
     * 获取自定义导出商品信息可选字段（管理员）
     */
    @GetMapping(value = "/getCustomExportFields")
    public R<List<CustomExportFieldVo>> getCustomExportFields() {
        return productService.getCustomExportFields();
    }

    /**
     * 自定义导出商品信息（管理员）
     */
    @PostMapping(value = "/customExport")
    public R<Void> customExport(@RequestBody CustomExportBo bo) {
        return productService.customExport(bo);
    }

    /**
     * sku上下架
     *
     * @param bo
     * @return
     * @throws RStatusCodeException
     */
    @PostMapping("skuShelf")
    public R<Void> skuShelf(@RequestBody ProductSkuShelfBo bo) throws RStatusCodeException {
        if(null == bo.getProductSkuId() || null == bo.getIsOnShelf() || null == bo.getProductId() || null == bo.getProductSkuId()){
            return R.fail("参数错误");
        }
        productService.skuShelf(bo);
        return R.ok();
    }

    @PostMapping("synchronizationProductToEs")
    public void synchronizationProductToEs(@RequestParam String productCode) {
        productService.synchronizationProductToEs(productCode);
    }

    @PostMapping(value ="/productRelatedDataCopy")
    public R productRelatedDataCopy(@RequestBody ProductRelatedDataCopyVo productRelatedDataCopyVo) {
        return productService.productRelatedDataCopy(productRelatedDataCopyVo);
    }
    @GetMapping("/getSysUsers")
    public R<List<SysTenantVo>> getSysTenants() {
        return R.ok(sysTenantService.getSupTenants());
    }
}
