package com.zsmall.order.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.utils.MapstructUtils;
import com.hengjian.common.core.utils.StringUtils;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.domain.SysTenant;
import com.zsmall.bma.open.member.service.RuleLevelProductPriceV2Service;
import com.zsmall.common.domain.dto.*;
import com.zsmall.common.domain.tiktok.domain.dto.address.SiteMsgBo;
import com.zsmall.common.domain.tiktok.domain.dto.address.TikTokRecipientAddress;
import com.zsmall.common.enums.common.ChannelTypeEnum;
import com.zsmall.common.enums.order.LogisticsProgress;
import com.zsmall.common.enums.order.OrderExceptionEnum;
import com.zsmall.common.enums.order.OrderStateType;
import com.zsmall.common.enums.order.OrderStatusEnum;
import com.zsmall.common.enums.orderItem.ShippingOrderStateEnum;
import com.zsmall.order.biz.service.IProductAboutManger;
import com.zsmall.order.biz.service.OrderItemService;
import com.zsmall.order.entity.domain.OrderItem;
import com.zsmall.order.entity.domain.Orders;
import com.zsmall.order.entity.domain.bo.OrderItemBo;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderDTO;
import com.zsmall.order.entity.domain.dto.ErpSaleOrderItemDTO;
import com.zsmall.order.entity.domain.vo.OrderItemVo;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.mapper.OrderItemMapper;
import com.zsmall.product.entity.domain.Product;
import com.zsmall.product.entity.domain.ProductSku;
import com.zsmall.product.entity.domain.ProductSkuPrice;
import com.zsmall.product.entity.domain.RuleLevelProductPrice;
import com.zsmall.product.entity.iservice.IProductService;
import com.zsmall.product.entity.iservice.IProductSkuPriceService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.domain.SiteCountryCurrency;
import com.zsmall.system.entity.domain.TenantSalesChannel;
import com.zsmall.system.entity.iservice.ISiteCountryCurrencyService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 子订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-07
 */
@RequiredArgsConstructor
@Service
public class OrderItemServiceImpl implements OrderItemService {
    private final ISiteCountryCurrencyService iSiteCountryCurrencyService;
    private final OrderItemMapper baseMapper;
    private final IProductSkuPriceService productSkuPriceService;
    private final IProductAboutManger productAboutManger;
    private final ITenantSalesChannelService tenantSalesChannelService;
    private final IProductService productService;
    private final IProductSkuService productSkuService;
    private final ITenantSalesChannelService iTenantSalesChannelService;
    private final RuleLevelProductPriceV2Service ruleLevelProductPriceService;
    private final IProductService iProductService;
    private final IProductSkuService iProductSkuService;
    private final IProductSkuPriceService iProductSkuPriceService;
    private final IOrderItemService iOrderItemService;
    /**
     * 查询子订单
     */
    @Override
    public OrderItemVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询子订单列表
     */
    @Override
    public TableDataInfo<OrderItemVo> queryPageList(OrderItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OrderItem> lqw = buildQueryWrapper(bo);
        Page<OrderItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询子订单列表
     */
    @Override
    public List<OrderItemVo> queryList(OrderItemBo bo) {
        LambdaQueryWrapper<OrderItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OrderItem> buildQueryWrapper(OrderItemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OrderItem> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSupplierTenantId()), OrderItem::getSupplierTenantId, bo.getSupplierTenantId());
        lqw.eq(bo.getOrderId() != null, OrderItem::getOrderId, bo.getOrderId());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderItemNo()), OrderItem::getOrderItemNo, bo.getOrderItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelType()), OrderItem::getChannelType, bo.getChannelType());
        lqw.eq(bo.getChannelId() != null, OrderItem::getChannelId, bo.getChannelId());
        lqw.eq(StringUtils.isNotBlank(bo.getChannelItemNo()), OrderItem::getChannelItemNo, bo.getChannelItemNo());
        lqw.eq(StringUtils.isNotBlank(bo.getStockManager()), OrderItem::getStockManager, bo.getStockManager());
        lqw.eq(StringUtils.isNotBlank(bo.getLogisticsType()), OrderItem::getLogisticsType, bo.getLogisticsType());
        lqw.eq(StringUtils.isNotBlank(bo.getShippingOrderState()), OrderItem::getShippingOrderState, bo.getShippingOrderState());
        lqw.eq(StringUtils.isNotBlank(bo.getFulfillmentProgress()), OrderItem::getFulfillmentProgress, bo.getFulfillmentProgress());
        lqw.eq(bo.getDispatchedTime() != null, OrderItem::getDispatchedTime, bo.getDispatchedTime());
        lqw.eq(bo.getFulfillmentTime() != null, OrderItem::getFulfillmentTime, bo.getFulfillmentTime());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderState()), OrderItem::getOrderState, bo.getOrderState());
        lqw.eq(StringUtils.isNotBlank(bo.getProductSkuCode()), OrderItem::getProductSkuCode, bo.getProductSkuCode());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityType()), OrderItem::getActivityType, bo.getActivityType());
        lqw.eq(StringUtils.isNotBlank(bo.getActivityCode()), OrderItem::getActivityCode, bo.getActivityCode());
        lqw.eq(bo.getTotalQuantity() != null, OrderItem::getTotalQuantity, bo.getTotalQuantity());
        lqw.eq(bo.getRestockQuantity() != null, OrderItem::getRestockQuantity, bo.getRestockQuantity());
        lqw.eq(bo.getSupplierIncomeEarned() != null, OrderItem::getSupplierIncomeEarned, bo.getSupplierIncomeEarned());
        lqw.eq(bo.getOriginalPayableUnitPrice() != null, OrderItem::getOriginalPayableUnitPrice, bo.getOriginalPayableUnitPrice());
        lqw.eq(bo.getOriginalPayableTotalAmount() != null, OrderItem::getOriginalPayableTotalAmount, bo.getOriginalPayableTotalAmount());
        lqw.eq(bo.getOriginalActualUnitPrice() != null, OrderItem::getOriginalActualUnitPrice, bo.getOriginalActualUnitPrice());
        lqw.eq(bo.getOriginalActualTotalAmount() != null, OrderItem::getOriginalActualTotalAmount, bo.getOriginalActualTotalAmount());
        lqw.eq(bo.getOriginalRefundExecutableAmount() != null, OrderItem::getOriginalRefundExecutableAmount, bo.getOriginalRefundExecutableAmount());
        lqw.eq(bo.getPlatformPayableUnitPrice() != null, OrderItem::getPlatformPayableUnitPrice, bo.getPlatformPayableUnitPrice());
        lqw.eq(bo.getPlatformPayableTotalAmount() != null, OrderItem::getPlatformPayableTotalAmount, bo.getPlatformPayableTotalAmount());
        lqw.eq(bo.getPlatformActualUnitPrice() != null, OrderItem::getPlatformActualUnitPrice, bo.getPlatformActualUnitPrice());
        lqw.eq(bo.getPlatformActualTotalAmount() != null, OrderItem::getPlatformActualTotalAmount, bo.getPlatformActualTotalAmount());
        lqw.eq(bo.getPlatformRefundExecutableAmount() != null, OrderItem::getPlatformRefundExecutableAmount, bo.getPlatformRefundExecutableAmount());
        lqw.eq(bo.getChannelSaleUnitPrice() != null, OrderItem::getChannelSaleUnitPrice, bo.getChannelSaleUnitPrice());
        lqw.eq(bo.getChannelSaleTotalAmount() != null, OrderItem::getChannelSaleTotalAmount, bo.getChannelSaleTotalAmount());
        return lqw;
    }

    /**
     * 新增子订单
     */
    @Override
    public Boolean insertByBo(OrderItemBo bo) {
        OrderItem add = MapstructUtils.convert(bo, OrderItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改子订单
     */
    @Override
    public Boolean updateByBo(OrderItemBo bo) {
        OrderItem update = MapstructUtils.convert(bo, OrderItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OrderItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除子订单
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public OrderItem setOrderBusinessField(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,
                                           Orders orders, SaleOrderItemDTO saleOrderItemDTO) {
        SaleOrderItemDTO itemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        Long siteId = orders.getSiteId();
        String currency = orders.getCurrency();
        String currencySymbol = orders.getCurrencySymbol();
        SiteCountryCurrency siteById = iSiteCountryCurrencyService.getSiteById(siteId);
        orderItem.setCountryCode(siteById.getCountryCode());
        orderItem.setCurrency(currency);
        orderItem.setCurrencySymbol(currencySymbol);
        orderItem.setSiteId(siteId);

        String erpSku = itemDTO.getErpSku();
        ProductSkuPrice price = null;
        if (ChannelTypeEnum.TikTok.name().equals(orders.getChannelType().name())) {
            price = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCodeAndSite(erpSku, siteId));
        }
        if (ChannelTypeEnum.Erp.name().equals(orders.getChannelType().name())) {
            price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSku(erpSku, siteId));
        }

        ProductSkuPrice finalPrice = price;
        ProductSku sku = TenantHelper.ignore(()->iProductSkuService.getById(finalPrice.getProductSkuId()));
        Product product = TenantHelper.ignore(()->iProductService.getById(sku.getProductId())) ;
//        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();
//        String regionCode = null;
//        Long siteId = null;
//        if(ObjectUtil.isNotEmpty(address) && StringUtils.isNotEmpty(address.getRegionCode())){
//            regionCode = address.getRegionCode();
//            SiteCountryCurrency siteByCountryCode = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);
//            siteId = siteByCountryCode.getId();
//        }else {
//            SiteCountryCurrency siteByCountryCode = iSiteCountryCurrencyService.getSiteByCountryCode("US");
//            siteId = siteByCountryCode.getId();
//        }

        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), orderReceiveFromThirdDTO.getTenantId(), price.getProductSkuId(), siteId);

        orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
        orderItem.setRestockQuantity(0);

        String status = getOrderStatus(orderReceiveFromThirdDTO, orders);

        orderItem.setOrderState(OrderStateType.getByName(status));
        orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orderItem.setDispatchedTime(null);
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityCode(null);

        BigDecimal totalPrice = getTotalPriceByChannelType(orderItem.getChannelType()
                                                                    .name(), BigDecimal.ZERO, itemDTO, price);

        BigDecimal originalTotalPriceForTikTok = null;
        if(ObjectUtil.isNotEmpty(memberPrice)){
            originalTotalPriceForTikTok = memberPrice.getOriginalUnitPrice();
        }else{
            originalTotalPriceForTikTok = price.getOriginalUnitPrice();
        }

        BigDecimal originalTotalPrice = originalTotalPriceForTikTok
                                             .multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

        // 产品价格-分销商 -拿产品表价格

        orderItem.setSupplierIncomeEarned(totalPrice);
        BigDecimal originalPayableUnitPriceForTiktok = null;
        // 会员定价
        if(ObjectUtil.isNotEmpty(memberPrice)){
            originalPayableUnitPriceForTiktok = memberPrice.getOriginalUnitPrice();
        }else{
            originalPayableUnitPriceForTiktok = price.getOriginalUnitPrice();
        }
//        orderItem.setProductU
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPriceForTiktok);
        orderItem.setOriginalPayableTotalAmount(totalPrice);
        // 此处为0是供应商不在分销平台内看到实际的售额
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        // x
        orderItem.setOriginalActualTotalAmount(originalTotalPrice);
        orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
        // 价格/数量 取的是订单价格
//        BigDecimal platformTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
        // 单价

        BigDecimal platformTotalAmount = getPlatformTotalAmount(orderItem.getChannelType()
                                                                         .name(), BigDecimal.ZERO, itemDTO, price, orderReceiveFromThirdDTO);
        BigDecimal platformPayableUnitPrice = null;

        if(ObjectUtil.isNotEmpty(memberPrice)){
            platformPayableUnitPrice = memberPrice.getPlatformUnitPrice();
        }else{
            platformPayableUnitPrice = price.getPlatformUnitPrice();
        }
        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPayableTotalAmount(platformTotalAmount);
        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);

        BigDecimal unitPrice = getUnitPrice(orderItem.getChannelType()
                                                     .name(), BigDecimal.ZERO, itemDTO, price, orderReceiveFromThirdDTO, memberPrice);
        orderItem.setPlatformActualUnitPrice(unitPrice);

        orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
        orderItem.setChannelSaleUnitPrice(unitPrice);
        orderItem.setChannelSaleTotalAmount(platformTotalAmount);
        orderItem.setPlatformActualTotalAmount(platformTotalAmount);
        return orderItem;
    }

    @Override
    public OrderItem setOrderBusinessFieldForOpen(OrderItem orderItem,
                                                  OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders,
                                                  SaleOrderItemDTO saleOrderItemDTO) {
        SaleOrderItemDTO itemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        String erpSku = itemDTO.getErpSku();
        String itemNo = itemDTO.getItemNo();
        ProductSkuPrice price = null;
        Long siteId = orders.getSiteId();
        price = TenantHelper.ignore(() -> productAboutManger.getProductPriceByItemNo(itemNo, siteId));

        ProductSkuPrice finalPrice = price;
        ProductSku sku = TenantHelper.ignore(()->iProductSkuService.getById(finalPrice.getProductSkuId()));
        Product product = TenantHelper.ignore(()->iProductService.getById(sku.getProductId()));


        RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), orderReceiveFromThirdDTO.getTenantId(), price.getProductSkuId(), siteId);
        orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
        orderItem.setRestockQuantity(0);

        String status = getOrderStatus(orderReceiveFromThirdDTO, orders);

        orderItem.setOrderState(OrderStateType.getByName(status));
        orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orderItem.setDispatchedTime(null);
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityCode(null);

//        BigDecimal totalPrice = price.getOriginalUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
        // 销售额
        if(ObjectUtil.isEmpty(orderItem.getChannelType())){
            orderItem.setChannelType(ChannelTypeEnum.Others);
        }

        BigDecimal totalPrice = getTotalPriceByChannelType(orderItem.getChannelType()
                                                                    .name(), BigDecimal.ZERO, itemDTO, price);

        // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
//        BigDecimal totalPrice = itemDTO.getUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

//        BigDecimal originalUnitPrice = getOriginalUnitPrice(ChannelTypeEnum.Erp.name(),BigDecimal.ZERO, itemDTO, price);
        // 平台价格
        BigDecimal originalTotalPriceForOpen = null;
        if(ObjectUtil.isNotEmpty(memberPrice)){
            originalTotalPriceForOpen = memberPrice.getOriginalUnitPrice();
        }else{
            originalTotalPriceForOpen = price.getOriginalUnitPrice();
        }
        BigDecimal originalTotalPrice = originalTotalPriceForOpen
                                             .multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

        // 产品价格-分销商 -拿产品表价格

        orderItem.setSupplierIncomeEarned(totalPrice);
        // 会员定价
        BigDecimal originalPayableUnitPriceForOpen = null;
        if(ObjectUtil.isNotEmpty(memberPrice)){
            originalPayableUnitPriceForOpen = memberPrice.getOriginalUnitPrice();
        }else{
            originalPayableUnitPriceForOpen = price.getOriginalUnitPrice();
        }
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPriceForOpen);
        orderItem.setOriginalPayableTotalAmount(totalPrice);
        // 此处为0是供应商不在分销平台内看到实际的售额
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        // x
        orderItem.setOriginalActualTotalAmount(originalTotalPrice);

        // 价格/数量 取的是订单价格
//        BigDecimal platformTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
        // 单价

        BigDecimal platformTotalAmount = getPlatformTotalAmount(orderItem.getChannelType()
                                                                         .name(), BigDecimal.ZERO, itemDTO, price, orderReceiveFromThirdDTO);

        BigDecimal platformPayableUnitPrice = null;

        if(ObjectUtil.isNotEmpty(memberPrice)){
            platformPayableUnitPrice = memberPrice.getPlatformUnitPrice();
        }else{
            platformPayableUnitPrice = price.getPlatformUnitPrice();
        }

        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPayableTotalAmount(platformTotalAmount);
        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);


        BigDecimal unitPrice = getUnitPrice(orderItem.getChannelType()
                                                     .name(), BigDecimal.ZERO, itemDTO, price, orderReceiveFromThirdDTO,memberPrice);

        orderItem.setPlatformActualUnitPrice(unitPrice);
        orderItem.setOriginalRefundExecutableAmount(originalTotalPrice);
        orderItem.setPlatformRefundExecutableAmount(originalTotalPrice);
        orderItem.setChannelSaleUnitPrice(unitPrice);
        orderItem.setChannelSaleTotalAmount(platformTotalAmount);
        orderItem.setPlatformActualTotalAmount(platformTotalAmount);

        orderItem.setSiteId(siteId);
        orderItem.setCurrency(orders.getCurrency());
        orderItem.setCountryCode(orders.getCountryCode());
        orderItem.setCurrencySymbol(orders.getCurrencySymbol());
        return orderItem;
    }

    @Override
    public OrderItem setOrderBusinessFieldForTemu(OrderItem orderItem, TemuOrderDTO temuOrderDTO, Orders orders, TemuOrderItemDTO temuOrderItemDTO) {
//        ProductSkuPrice price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSkuNull(temuOrderItemDTO.getSku()));
        ProductSkuPrice price = new ProductSkuPrice();
        if(StringUtils.isNotEmpty(temuOrderItemDTO.getProductSkuCode())){
            price = TenantHelper.ignore(() ->iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(temuOrderItemDTO.getProductSkuCode(), temuOrderDTO.getCountry_code()));
        }
        if(null != price && null != price.getProductSkuId() ) {
            ProductSkuPrice finalPrice = price;
            ProductSku sku = TenantHelper.ignore(() -> iProductSkuService.getById(finalPrice.getProductSkuId()));
            Product product = TenantHelper.ignore(() -> iProductService.getById(sku.getProductId()));
            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), temuOrderDTO.getTenantId(), price.getProductSkuId(), null);
            orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
            orderItem.setRestockQuantity(0);

            String status = getOrderStatusByTemu(temuOrderDTO, orders);

            orderItem.setOrderState(OrderStateType.getByName(status));
            orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderItem.setDispatchedTime(null);
            orderItem.setFulfillmentTime(null);
            orderItem.setActivityCode(null);

//        BigDecimal totalPrice = price.getOriginalUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
            // 销售额
            if (ObjectUtil.isEmpty(orderItem.getChannelType())) {
                orderItem.setChannelType(ChannelTypeEnum.Others);
            }

            BigDecimal totalPrice = temuOrderDTO.getAmount();

            // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
//        BigDecimal totalPrice = itemDTO.getUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

//        BigDecimal originalUnitPrice = getOriginalUnitPrice(ChannelTypeEnum.Erp.name(),BigDecimal.ZERO, itemDTO, price);
            // 平台价格
            BigDecimal originalTotalPriceForOpen;
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalTotalPriceForOpen = memberPrice.getOriginalUnitPrice();
            } else {
                originalTotalPriceForOpen = price.getOriginalUnitPrice();
            }
            BigDecimal originalTotalPrice = BigDecimal.ZERO;
            if(null != originalTotalPriceForOpen){
                originalTotalPrice = originalTotalPriceForOpen
                    .multiply(BigDecimal.valueOf(temuOrderItemDTO.getQuantity()));
            }
            orderItem.setSupplierIncomeEarned(totalPrice);
            // 会员定价
            BigDecimal originalPayableUnitPriceForOpen = null;
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalPayableUnitPriceForOpen = memberPrice.getOriginalUnitPrice();
            } else {
                originalPayableUnitPriceForOpen = price.getOriginalUnitPrice();
            }
            orderItem.setOriginalPayableUnitPrice(originalPayableUnitPriceForOpen);
            orderItem.setOriginalPayableTotalAmount(totalPrice);
            // 此处为0是供应商不在分销平台内看到实际的售额
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // x
            orderItem.setOriginalActualTotalAmount(originalTotalPrice);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
            // 价格/数量 取的是订单价格
//        BigDecimal platformTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
            // 单价

            BigDecimal platformTotalAmount = totalPrice;

            BigDecimal platformPayableUnitPrice = null;

            if (ObjectUtil.isNotEmpty(memberPrice)) {
                platformPayableUnitPrice = memberPrice.getPlatformUnitPrice();
            } else {
                platformPayableUnitPrice = price.getPlatformUnitPrice();
            }

            orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);

//        orderItem.setPlatformPayableTotalAmount(platformTotalAmount);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);


            BigDecimal unitPrice = temuOrderItemDTO.getPrice();

            orderItem.setPlatformActualUnitPrice(unitPrice);

            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setChannelSaleUnitPrice(unitPrice);
            orderItem.setPlatformActualTotalAmount(platformTotalAmount);
        }else {

            orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
            orderItem.setRestockQuantity(0);

            String status = getOrderStatusByTemu(temuOrderDTO, orders);

            orderItem.setOrderState(OrderStateType.getByName(status));
            orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderItem.setDispatchedTime(null);
            orderItem.setFulfillmentTime(null);
            orderItem.setActivityCode(null);

            if (ObjectUtil.isEmpty(orderItem.getChannelType())) {
                orderItem.setChannelType(ChannelTypeEnum.Others);
            }

            BigDecimal totalPrice = temuOrderDTO.getAmount();

            // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
            // 平台价格
            BigDecimal originalTotalPrice;
            if(null != temuOrderItemDTO.getPrice() && null != temuOrderItemDTO.getQuantity()) {
                originalTotalPrice = temuOrderItemDTO.getPrice()
                                                                .multiply(BigDecimal.valueOf(temuOrderItemDTO.getQuantity()));
            }else {
                originalTotalPrice = temuOrderDTO.getAmount();
            }

            // 产品价格-分销商 -拿产品表价格

            orderItem.setSupplierIncomeEarned(totalPrice);
            // 会员定价
            orderItem.setOriginalPayableUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPayableTotalAmount(totalPrice);
            // 此处为0是供应商不在分销平台内看到实际的售额
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // x
            orderItem.setOriginalActualTotalAmount(originalTotalPrice);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);

            orderItem.setPlatformPayableUnitPrice(BigDecimal.ZERO);

            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);

            BigDecimal unitPrice = temuOrderItemDTO.getPrice();

            orderItem.setPlatformActualUnitPrice(unitPrice);

            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setChannelSaleUnitPrice(unitPrice);
        }
        return orderItem;
    }

    @Override
    public OrderItem setOrderBusinessFieldForAmazonVc(OrderItem orderItem,
                                                      AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders,
                                                      AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO) {
        //        ProductSkuPrice price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSkuNull(temuOrderItemDTO.getSku()));
        ProductSkuPrice price = new ProductSkuPrice();
        if(StringUtils.isNotEmpty(amazonVCOrderItemMessageDTO.getProductSkuCode())){
            price = TenantHelper.ignore(() ->iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(amazonVCOrderItemMessageDTO.getProductSkuCode(), amazonVCOrderMessageDTO.getCountry_code()));
        }
        if(null != price && null != price.getProductSkuId() ) {
            ProductSkuPrice finalPrice = price;
            ProductSku sku = TenantHelper.ignore(() -> iProductSkuService.getById(finalPrice.getProductSkuId()));
            Product product = TenantHelper.ignore(() -> iProductService.getById(sku.getProductId()));
            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), amazonVCOrderMessageDTO.getTenantId(), price.getProductSkuId(), null);
            orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
            orderItem.setRestockQuantity(0);

            String status = getOrderStatusByAmazonVc(amazonVCOrderMessageDTO, orders);

            orderItem.setOrderState(OrderStateType.getByName(status));
            orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderItem.setDispatchedTime(null);
            orderItem.setFulfillmentTime(null);
            orderItem.setActivityCode(null);

//        BigDecimal totalPrice = price.getOriginalUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
            // 销售额
            if (ObjectUtil.isEmpty(orderItem.getChannelType())) {
                orderItem.setChannelType(ChannelTypeEnum.Others);
            }

            BigDecimal totalPrice = amazonVCOrderMessageDTO.getAmount();

            // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
//        BigDecimal totalPrice = itemDTO.getUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

//        BigDecimal originalUnitPrice = getOriginalUnitPrice(ChannelTypeEnum.Erp.name(),BigDecimal.ZERO, itemDTO, price);
            // 平台价格
            BigDecimal originalTotalPriceForOpen;
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalTotalPriceForOpen = memberPrice.getOriginalUnitPrice();
            } else {
                originalTotalPriceForOpen = price.getOriginalUnitPrice();
            }
            BigDecimal originalTotalPrice = BigDecimal.ZERO;
            if(null != originalTotalPriceForOpen){
                originalTotalPrice = originalTotalPriceForOpen
                    .multiply(BigDecimal.valueOf(amazonVCOrderItemMessageDTO.getQuantity()));
            }
            orderItem.setSupplierIncomeEarned(totalPrice);
            // 会员定价
            BigDecimal originalPayableUnitPriceForOpen = null;
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalPayableUnitPriceForOpen = memberPrice.getOriginalUnitPrice();
            } else {
                originalPayableUnitPriceForOpen = price.getOriginalUnitPrice();
            }
            orderItem.setOriginalPayableUnitPrice(originalPayableUnitPriceForOpen);
            orderItem.setOriginalPayableTotalAmount(totalPrice);
            // 此处为0是供应商不在分销平台内看到实际的售额
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // x
            orderItem.setOriginalActualTotalAmount(originalTotalPrice);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
            // 价格/数量 取的是订单价格
//        BigDecimal platformTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
            // 单价

            BigDecimal platformTotalAmount = totalPrice;

            BigDecimal platformPayableUnitPrice = null;

            if (ObjectUtil.isNotEmpty(memberPrice)) {
                platformPayableUnitPrice = memberPrice.getPlatformUnitPrice();
            } else {
                platformPayableUnitPrice = price.getPlatformUnitPrice();
            }

            orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);

//        orderItem.setPlatformPayableTotalAmount(platformTotalAmount);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);


            BigDecimal unitPrice = amazonVCOrderItemMessageDTO.getPrice();

            orderItem.setPlatformActualUnitPrice(unitPrice);

            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setChannelSaleUnitPrice(unitPrice);
            orderItem.setPlatformActualTotalAmount(platformTotalAmount);
        }else {

            orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
            orderItem.setRestockQuantity(0);

            String status = getOrderStatusByAmazonVc(amazonVCOrderMessageDTO, orders);

            orderItem.setOrderState(OrderStateType.getByName(status));
            orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderItem.setDispatchedTime(null);
            orderItem.setFulfillmentTime(null);
            orderItem.setActivityCode(null);

            if (ObjectUtil.isEmpty(orderItem.getChannelType())) {
                orderItem.setChannelType(ChannelTypeEnum.Others);
            }

            BigDecimal totalPrice = amazonVCOrderMessageDTO.getAmount();

            // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
            // 平台价格
            BigDecimal originalTotalPrice;
            if(null != amazonVCOrderItemMessageDTO.getPrice() && null != amazonVCOrderItemMessageDTO.getQuantity()) {
                originalTotalPrice = amazonVCOrderItemMessageDTO.getPrice()
                                                     .multiply(BigDecimal.valueOf(amazonVCOrderItemMessageDTO.getQuantity()));
            }else {
                originalTotalPrice = amazonVCOrderMessageDTO.getAmount();
            }

            // 产品价格-分销商 -拿产品表价格

            orderItem.setSupplierIncomeEarned(totalPrice);
            // 会员定价
            orderItem.setOriginalPayableUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPayableTotalAmount(totalPrice);
            // 此处为0是供应商不在分销平台内看到实际的售额
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // x
            orderItem.setOriginalActualTotalAmount(originalTotalPrice);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);

            orderItem.setPlatformPayableUnitPrice(BigDecimal.ZERO);

            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);

            BigDecimal unitPrice = amazonVCOrderItemMessageDTO.getPrice();

            orderItem.setPlatformActualUnitPrice(unitPrice);

            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setChannelSaleUnitPrice(unitPrice);
        }
        return orderItem;
    }

    @Override
    public OrderItem setOrderBusinessFieldForEc(OrderItem orderItem, EcOrderMessageDTO ecOrderMessageDTO, Orders orders, EcOrderItemMessageDTO ecOrderItemMessageDTO) {
        //        ProductSkuPrice price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSkuNull(temuOrderItemDTO.getSku()));
        ProductSkuPrice price = new ProductSkuPrice();
        if(StringUtils.isNotEmpty(ecOrderItemMessageDTO.getProductSkuCode())){
            price = TenantHelper.ignore(() ->iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(ecOrderItemMessageDTO.getProductSkuCode(), ecOrderMessageDTO.getCountry_code()));
        }
        if(null != price && null != price.getProductSkuId() ) {
            ProductSkuPrice finalPrice = price;
            ProductSku sku = TenantHelper.ignore(() -> iProductSkuService.getById(finalPrice.getProductSkuId()));
            Product product = TenantHelper.ignore(() -> iProductService.getById(sku.getProductId()));
            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), ecOrderMessageDTO.getTenantId(), price.getProductSkuId(),null );
            orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
            orderItem.setRestockQuantity(0);

            String status = getOrderStatusByEc(ecOrderMessageDTO, orders);

            orderItem.setOrderState(OrderStateType.getByName(status));
            orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderItem.setDispatchedTime(null);
            orderItem.setFulfillmentTime(null);
            orderItem.setActivityCode(null);

//        BigDecimal totalPrice = price.getOriginalUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
            // 销售额
            if (ObjectUtil.isEmpty(orderItem.getChannelType())) {
                orderItem.setChannelType(ChannelTypeEnum.Others);
            }

            BigDecimal totalPrice = ecOrderMessageDTO.getAmount();

            // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
//        BigDecimal totalPrice = itemDTO.getUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

//        BigDecimal originalUnitPrice = getOriginalUnitPrice(ChannelTypeEnum.Erp.name(),BigDecimal.ZERO, itemDTO, price);
            // 平台价格
            BigDecimal originalTotalPriceForOpen;
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalTotalPriceForOpen = memberPrice.getOriginalUnitPrice();
            } else {
                originalTotalPriceForOpen = price.getOriginalUnitPrice();
            }
            BigDecimal originalTotalPrice = BigDecimal.ZERO;
            if(null != originalTotalPriceForOpen){
                originalTotalPrice = originalTotalPriceForOpen
                    .multiply(BigDecimal.valueOf(ecOrderItemMessageDTO.getQuantity()));
            }
            orderItem.setSupplierIncomeEarned(totalPrice);
            // 会员定价
            BigDecimal originalPayableUnitPriceForOpen = null;
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalPayableUnitPriceForOpen = memberPrice.getOriginalUnitPrice();
            } else {
                originalPayableUnitPriceForOpen = price.getOriginalUnitPrice();
            }
            orderItem.setOriginalPayableUnitPrice(originalPayableUnitPriceForOpen);
            orderItem.setOriginalPayableTotalAmount(totalPrice);
            // 此处为0是供应商不在分销平台内看到实际的售额
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // x
            orderItem.setOriginalActualTotalAmount(originalTotalPrice);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
            // 价格/数量 取的是订单价格
//        BigDecimal platformTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
            // 单价

            BigDecimal platformTotalAmount = totalPrice;

            BigDecimal platformPayableUnitPrice = null;

            if (ObjectUtil.isNotEmpty(memberPrice)) {
                platformPayableUnitPrice = memberPrice.getPlatformUnitPrice();
            } else {
                platformPayableUnitPrice = price.getPlatformUnitPrice();
            }

            orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);

//        orderItem.setPlatformPayableTotalAmount(platformTotalAmount);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);


            BigDecimal unitPrice = ecOrderItemMessageDTO.getSubtotal();

            orderItem.setPlatformActualUnitPrice(unitPrice);

            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setChannelSaleUnitPrice(unitPrice);
            orderItem.setPlatformActualTotalAmount(platformTotalAmount);
        }else {

            orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
            orderItem.setRestockQuantity(0);

            String status = getOrderStatusByEc(ecOrderMessageDTO, orders);

            orderItem.setOrderState(OrderStateType.getByName(status));
            orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderItem.setDispatchedTime(null);
            orderItem.setFulfillmentTime(null);
            orderItem.setActivityCode(null);

            if (ObjectUtil.isEmpty(orderItem.getChannelType())) {
                orderItem.setChannelType(ChannelTypeEnum.Others);
            }

            BigDecimal totalPrice = ecOrderMessageDTO.getAmount();

            // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
            // 平台价格
            BigDecimal originalTotalPrice;
            if(null != ecOrderItemMessageDTO.getSubtotal() && null != ecOrderItemMessageDTO.getQuantity()) {
                originalTotalPrice = ecOrderItemMessageDTO.getSubtotal()
                                                                .multiply(BigDecimal.valueOf(ecOrderItemMessageDTO.getQuantity()));
            }else {
                originalTotalPrice = ecOrderMessageDTO.getAmount();
            }

            // 产品价格-分销商 -拿产品表价格

            orderItem.setSupplierIncomeEarned(totalPrice);
            // 会员定价
            orderItem.setOriginalPayableUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPayableTotalAmount(totalPrice);
            // 此处为0是供应商不在分销平台内看到实际的售额
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // x
            orderItem.setOriginalActualTotalAmount(originalTotalPrice);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);

            orderItem.setPlatformPayableUnitPrice(BigDecimal.ZERO);

            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);

            BigDecimal unitPrice = ecOrderItemMessageDTO.getSubtotal();

            orderItem.setPlatformActualUnitPrice(unitPrice);

            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setChannelSaleUnitPrice(unitPrice);
        }
        return orderItem;
    }

    @Override
    public OrderItem setOrderBusinessFieldForAmazonSc(OrderItem orderItem, AmazonScOrderDTO amazonScOrderDTO, Orders orders, AmazonScOrderItemDTO amazonScOrderItemDTO) {
        //        ProductSkuPrice price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSkuNull(temuOrderItemDTO.getSku()));
        ProductSkuPrice price = new ProductSkuPrice();
        if(StringUtils.isNotEmpty(amazonScOrderItemDTO.getProductSkuCode())){
            price = TenantHelper.ignore(() ->iProductSkuPriceService.queryByProductSkuCodeAndCountryCode(amazonScOrderItemDTO.getProductSkuCode(), amazonScOrderDTO.getCountry_code()));
        }
        if(null != price && null != price.getProductSkuId() ) {
            ProductSkuPrice finalPrice = price;
            ProductSku sku = TenantHelper.ignore(() -> iProductSkuService.getById(finalPrice.getProductSkuId()));
            Product product = TenantHelper.ignore(() -> iProductService.getById(sku.getProductId()));
            RuleLevelProductPrice memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), amazonScOrderDTO.getTenantId(), price.getProductSkuId(), null);
            orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
            orderItem.setRestockQuantity(0);

            String status = getOrderStatusByAmazonSc(amazonScOrderDTO, orders);

            orderItem.setOrderState(OrderStateType.getByName(status));
            orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderItem.setDispatchedTime(null);
            orderItem.setFulfillmentTime(null);
            orderItem.setActivityCode(null);

//        BigDecimal totalPrice = price.getOriginalUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
            // 销售额
            if (ObjectUtil.isEmpty(orderItem.getChannelType())) {
                orderItem.setChannelType(ChannelTypeEnum.Others);
            }

            BigDecimal totalPrice = amazonScOrderDTO.getAmount();

            // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
//        BigDecimal totalPrice = itemDTO.getUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

//        BigDecimal originalUnitPrice = getOriginalUnitPrice(ChannelTypeEnum.Erp.name(),BigDecimal.ZERO, itemDTO, price);
            // 平台价格
            BigDecimal originalTotalPriceForOpen;
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalTotalPriceForOpen = memberPrice.getOriginalUnitPrice();
            } else {
                originalTotalPriceForOpen = price.getOriginalUnitPrice();
            }
            BigDecimal originalTotalPrice = BigDecimal.ZERO;
            if(null != originalTotalPriceForOpen){
                originalTotalPrice = originalTotalPriceForOpen
                    .multiply(BigDecimal.valueOf(amazonScOrderItemDTO.getQuantity()));
            }
            orderItem.setSupplierIncomeEarned(totalPrice);
            // 会员定价
            BigDecimal originalPayableUnitPriceForOpen = null;
            if (ObjectUtil.isNotEmpty(memberPrice)) {
                originalPayableUnitPriceForOpen = memberPrice.getOriginalUnitPrice();
            } else {
                originalPayableUnitPriceForOpen = price.getOriginalUnitPrice();
            }
            orderItem.setOriginalPayableUnitPrice(originalPayableUnitPriceForOpen);
            orderItem.setOriginalPayableTotalAmount(totalPrice);
            // 此处为0是供应商不在分销平台内看到实际的售额
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // x
            orderItem.setOriginalActualTotalAmount(originalTotalPrice);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
            // 价格/数量 取的是订单价格
//        BigDecimal platformTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
            // 单价

            BigDecimal platformTotalAmount = totalPrice;

            BigDecimal platformPayableUnitPrice = null;

            if (ObjectUtil.isNotEmpty(memberPrice)) {
                platformPayableUnitPrice = memberPrice.getPlatformUnitPrice();
            } else {
                platformPayableUnitPrice = price.getPlatformUnitPrice();
            }

            orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);

//        orderItem.setPlatformPayableTotalAmount(platformTotalAmount);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);


            BigDecimal unitPrice = BigDecimal.ZERO;
//                amazonScOrderItemDTO.getPrice();

            orderItem.setPlatformActualUnitPrice(unitPrice);

            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setChannelSaleUnitPrice(unitPrice);
            orderItem.setPlatformActualTotalAmount(platformTotalAmount);
        }else {

            orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
            orderItem.setRestockQuantity(0);

            String status = getOrderStatusByAmazonSc(amazonScOrderDTO, orders);

            orderItem.setOrderState(OrderStateType.getByName(status));
            orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
            orderItem.setDispatchedTime(null);
            orderItem.setFulfillmentTime(null);
            orderItem.setActivityCode(null);

            if (ObjectUtil.isEmpty(orderItem.getChannelType())) {
                orderItem.setChannelType(ChannelTypeEnum.Others);
            }

            BigDecimal totalPrice = amazonScOrderDTO.getAmount();

            // 因为订单接口拿不到产品数量了,所以建议直接用产品sku总价来放入
            // 平台价格
            BigDecimal originalTotalPrice = BigDecimal.ZERO;

//                temuOrderItemDTO.getPrice()
//                                                           .multiply(BigDecimal.valueOf(amazonScOrderItemDTO.getQuantity()));

            // 产品价格-分销商 -拿产品表价格

            orderItem.setSupplierIncomeEarned(totalPrice);
            // 会员定价
            orderItem.setOriginalPayableUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPayableTotalAmount(totalPrice);
            // 此处为0是供应商不在分销平台内看到实际的售额
            orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
            // x
            orderItem.setOriginalActualTotalAmount(originalTotalPrice);
            orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);

            orderItem.setPlatformPayableUnitPrice(BigDecimal.ZERO);

            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
            orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);

            BigDecimal unitPrice = BigDecimal.ZERO;

//                amazonScOrderItemDTO.getPrice();

            orderItem.setPlatformActualUnitPrice(unitPrice);

            orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
            orderItem.setChannelSaleUnitPrice(unitPrice);
        }
        return orderItem;
    }

    /**
     * 功能描述：获取单价
     *
     * @param type                     类型
     * @param itemDTO                  项目 DTO
     * @param price                    价格
     * @param orderReceiveFromThirdDTO 从第三个 DTO 接收订单
     * @param memberPrice
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/02/23
     */
    private BigDecimal getUnitPrice(String type, BigDecimal unitPrice, SaleOrderItemDTO itemDTO, ProductSkuPrice price,
                                    OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,
                                    RuleLevelProductPrice memberPrice) {
        // todo 要改动
        if (ChannelTypeEnum.TikTok.name().equals(type)) {
            if(ObjectUtil.isNotEmpty(memberPrice)){
                unitPrice = memberPrice.getPlatformUnitPrice();
            }else {
                if(ObjectUtil.isNotEmpty(price)){
                    unitPrice = price.getPlatformUnitPrice();
                }else{
                    // 可能是后续补单业务
                    unitPrice = BigDecimal.ZERO;
                }


            }

        }
        if (ChannelTypeEnum.Erp.name().equals(type)) {
            //自己计算
            unitPrice = itemDTO.getUnitPrice();
        }
        return unitPrice;
    }

    /**
     * 功能描述：获取订单状态
     *
     * @param orderReceiveFromThirdDTO 从第三个 DTO 接收订单
     * @param orders                   订单
     * @return {@link String }
     * <AUTHOR>
     * @date 2024/02/07
     */
    private String getOrderStatus(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, Orders orders) {
        String orderStatus = orderReceiveFromThirdDTO.getOrderStatus();
        ChannelTypeEnum channelType = orders.getChannelType();
        String status;
        if (ChannelTypeEnum.TikTok.equals(channelType)) {
            if (ObjectUtil.isNotNull(orderStatus)) {
                status = OrderStatusEnum.getCodeByTikTokStatus(orderStatus)
                                        .getDistriButionStatus();
            } else {
                status = null;
            }
        } else {
            if(ObjectUtil.isNotEmpty(orderStatus)){
                status = OrderStatusEnum.getCodeByStatusDesc(orderStatus)
                                        .getDistriButionStatus();
            }
            return OrderStatusEnum.pending.getDistriButionStatus();
        }

        return status;
    }
    private String getOrderStatus(OrderReceiveFromThirdDTO orderReceiveFromThirdDTO, ChannelTypeEnum channelTypeEnum) {
        String orderStatus = orderReceiveFromThirdDTO.getOrderStatus();

        String status;
        if (ChannelTypeEnum.TikTok.equals(channelTypeEnum)) {
            if (ObjectUtil.isNotNull(orderStatus)) {
                status = OrderStatusEnum.getCodeByTikTokStatus(orderStatus)
                                        .getDistriButionStatus();
            } else {
                status = null;
            }
        } else {
            if(ObjectUtil.isNotEmpty(orderStatus)){
                status = OrderStatusEnum.getCodeByStatusDesc(orderStatus)
                                        .getDistriButionStatus();
            }
            return OrderStatusEnum.pending.getDistriButionStatus();
        }

        return status;
    }
    private String getOrderStatusByTemu(TemuOrderDTO temuOrderDTO, Orders orders) {
        String orderStatus = temuOrderDTO.getStatus();
        String status = null;
        if(ObjectUtil.isNotEmpty(orderStatus)){
            status = OrderStatusEnum.getCodeByStatusDesc(orderStatus)
                                    .getDistriButionStatus();
        }
        return status;
    }

    private String getOrderStatusByAmazonVc(AmazonVCOrderMessageDTO amazonVCOrderMessageDTO, Orders orders) {
        String orderStatus = amazonVCOrderMessageDTO.getStatus();
        String status = null;
        if(ObjectUtil.isNotEmpty(orderStatus)){
            status = OrderStatusEnum.getCodeByStatusDesc(orderStatus)
                                    .getDistriButionStatus();
        }
        return status;
    }

    private String getOrderStatusByEc(EcOrderMessageDTO ecOrderMessageDTO, Orders orders) {
        String orderStatus = ecOrderMessageDTO.getShipping_status();
        String status = null;
        if(ObjectUtil.isNotEmpty(orderStatus)){
            status = OrderStatusEnum.getCodeByStatusDesc(orderStatus)
                                    .getDistriButionStatus();
        }
        return status;
    }

    private String getOrderStatusByAmazonSc(AmazonScOrderDTO amazonScOrderDTO, Orders orders) {
        String orderStatus = amazonScOrderDTO.getStatus();
        String status = null;
        if(ObjectUtil.isNotEmpty(orderStatus)){
            status = OrderStatusEnum.getCodeByStatusDesc(orderStatus)
                                    .getDistriButionStatus();
        }
        return status;
    }


    /**
     * 功能描述：获取平台总金额
     *
     * @param name                     名字
     * @param platformTotalAmount      平台总金额
     * @param itemDTO                  项目 DTO
     * @param price                    价格
     * @param orderReceiveFromThirdDTO 从第三个 DTO 接收订单
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/02/07
     */
    private BigDecimal getPlatformTotalAmount(String name, BigDecimal platformTotalAmount, SaleOrderItemDTO itemDTO,
                                              ProductSkuPrice price,
                                              OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        // 销售额金额
        if (ChannelTypeEnum.TikTok.name().equals(name)) {
            platformTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
        }

        if (ChannelTypeEnum.Erp.name().equals(name)) {
            //自己计算
            platformTotalAmount = itemDTO.getUnitPrice().multiply(new BigDecimal(itemDTO.getQuantity()));
        }
        return platformTotalAmount;
    }


    private BigDecimal getOriginalUnitPrice(String name, BigDecimal originalUnitPrice, SaleOrderItemDTO itemDTO,
                                            ProductSkuPrice price) {
        if (ChannelTypeEnum.TikTok.name().equals(name)) {
            originalUnitPrice = itemDTO.getUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
        }
        if (ChannelTypeEnum.Erp.name().equals(name)) {
            originalUnitPrice = price.getOriginalUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
        }
        return null;
    }

    /**
     * 功能描述：按渠道类型获取总价
     *
     * @param name       名字
     * @param totalPrice 总价
     * @param itemDTO    项目 DTO
     * @param price      价格
     * @return {@link BigDecimal }
     * <AUTHOR>
     * @date 2024/02/07
     */
    private BigDecimal getTotalPriceByChannelType(String name, BigDecimal totalPrice, SaleOrderItemDTO itemDTO,
                                                  ProductSkuPrice price) {

        if (ChannelTypeEnum.TikTok.name().equals(name)) {
            totalPrice = itemDTO.getUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
        }
        if (ChannelTypeEnum.Erp.name().equals(name)) {
            totalPrice = price.getOriginalUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));
        }

        return totalPrice;
    }

    @Override
    public OrderItem setChannelTag(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,
                                   Orders orders,
                                   SaleOrderItemDTO saleOrderItemDTO) {
        SaleOrderItemDTO itemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        String sellerSku = itemDTO.getErpSku();
        ProductSku productSku;
        if (ChannelTypeEnum.TikTok.name().equals(orders.getChannelType().name())) {
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, sellerSku)));

        } else {
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, sellerSku)));
        }

        // 拿product的值
        orderItem.setChannelType(orderItem.getChannelType());
//        SysTenant sysTenant = baseMapper.getSysTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(), orderItem.getChannelType()
                                                                                                                                                     .name());
        orderItem.setChannelId(tenantSalesChannel.getId());
        orderItem.setSupplierTenantId(productSku.getTenantId());
        orderItem.setChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo());
        return orderItem;
    }

    @Override
    public OrderItem setChannelTagForOpen(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,
                                          Orders orders,
                                          SaleOrderItemDTO saleOrderItemDTO) {
        SaleOrderItemDTO itemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        String sellerSku = itemDTO.getErpSku();
        String itemNo = itemDTO.getItemNo();
        ProductSku productSku;
        productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, itemNo)));

        // 拿product的值
        orderItem.setChannelType(orderItem.getChannelType());
        SysTenant sysTenant = baseMapper.getSysTenantByThirdChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(),orders.getTenantId());
//        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(), orderItem.getChannelType()
//                                                                                                                                                     .name());
        orderItem.setChannelId(sysTenant.getId());
        orderItem.setSupplierTenantId(productSku.getTenantId());
        orderItem.setChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo());
        return orderItem;
    }

    @Override
    public OrderItem setChannelTagForTemu(OrderItem orderItem, TemuOrderDTO temuOrderDTO, Orders orders, TemuOrderItemDTO temuOrderItemDTO) {
//        TemuOrderItemDTO orderItemDTO = temuOrderDTO.getItems().get(0);
        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(temuOrderItemDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, temuOrderItemDTO.getProductSkuCode())));
        }
        // 拿product的值
//        orderItem.setChannelType(orders.getChannelType());
//        SysTenant sysTenant = baseMapper.getSysTenantByThirdChannelFlag(temuOrderDTO.getAccount_id());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(temuOrderDTO.getAccount_id(), orders.getChannelType().name());
        orderItem.setChannelId(tenantSalesChannel.getId());
        if(null != productSku && StringUtils.isNotEmpty(productSku.getTenantId())){
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
        orderItem.setChannelOrderNo(temuOrderDTO.getOrdernum());
        return orderItem;
    }

    @Override
    public OrderItem setChannelTagForAmazonVc(OrderItem orderItem, AmazonVCOrderMessageDTO amazonVCOrderMessageDTO,
                                              Orders orders, AmazonVCOrderItemMessageDTO amazonVCOrderItemMessageDTO) {

        //        TemuOrderItemDTO orderItemDTO = temuOrderDTO.getItems().get(0);
        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(amazonVCOrderItemMessageDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, amazonVCOrderItemMessageDTO.getProductSkuCode())));
        }
        // 拿product的值
//        orderItem.setChannelType(orders.getChannelType());
//        SysTenant sysTenant = baseMapper.getSysTenantByThirdChannelFlag(temuOrderDTO.getAccount_id());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(amazonVCOrderMessageDTO.getAccount_id(), orders.getChannelType().name());
        orderItem.setChannelId(tenantSalesChannel.getId());
        if(null != productSku && StringUtils.isNotEmpty(productSku.getTenantId())){
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
        orderItem.setChannelOrderNo(amazonVCOrderMessageDTO.getOrdernum());
        return orderItem;
    }

    @Override
    public OrderItem setChannelTagForAmazonEc(OrderItem orderItem, EcOrderMessageDTO ecOrderMessageDTO, Orders orders, EcOrderItemMessageDTO ecOrderItemMessageDTO) {

        //        TemuOrderItemDTO orderItemDTO = temuOrderDTO.getItems().get(0);
        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(ecOrderItemMessageDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, ecOrderItemMessageDTO.getProductSkuCode())));
        }
        // 拿product的值
//        orderItem.setChannelType(orders.getChannelType());
//        SysTenant sysTenant = baseMapper.getSysTenantByThirdChannelFlag(temuOrderDTO.getAccount_id());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(ecOrderMessageDTO.getAccount_id(), orders.getChannelType().name());
        orderItem.setChannelId(tenantSalesChannel.getId());
        if(null != productSku && StringUtils.isNotEmpty(productSku.getTenantId())){
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
        orderItem.setChannelOrderNo(ecOrderMessageDTO.getOrdernum());
        return orderItem;
    }

    @Override
    public OrderItem setChannelTagForAmazonSc(OrderItem orderItem, AmazonScOrderDTO amazonScOrderDTO, Orders orders,
                                              AmazonScOrderItemDTO amazonScOrderItemDTO) {
        ProductSku productSku = new ProductSku();
        if(StringUtils.isNotEmpty(amazonScOrderItemDTO.getProductSkuCode())){
            productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, amazonScOrderItemDTO.getProductSkuCode())));
        }
        // 拿product的值
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(amazonScOrderDTO.getAccount_id(), orders.getChannelType().name());
        orderItem.setChannelId(tenantSalesChannel.getId());
        if(null != productSku && StringUtils.isNotEmpty(productSku.getTenantId())){
            orderItem.setSupplierTenantId(productSku.getTenantId());
        }
        orderItem.setChannelOrderNo(amazonScOrderDTO.getOrdernum());
        return orderItem;
    }

    @Override
    public OrderItem setOrderBusinessField(OrderItem orderItem, ErpSaleOrderDTO erpSaleOrderDTO, Orders orders,
                                           ErpSaleOrderItemDTO saleOrderItemDTO) {
        ErpSaleOrderItemDTO itemDTO = erpSaleOrderDTO.getSaleOrderItemsList().get(0);
        String sellerSku = itemDTO.getSellerSku();
        Long siteId = orders.getSiteId();
        ProductSkuPrice price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSku(sellerSku,siteId));


        orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
        orderItem.setRestockQuantity(0);
        String status = OrderStatusEnum.getCodeByStatusDesc(erpSaleOrderDTO.getOrderStatus())
                                       .getDistriButionStatus();
        orderItem.setOrderState(OrderStateType.getByName(status));
        orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orderItem.setDispatchedTime(null);
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityCode(null);

        BigDecimal totalPrice = price.getOriginalUnitPrice().multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

        // 产品价格-分销商 -拿产品表价格 ❌ 拿推送订单的 sales_total_amount价格为准
//        orderItem.setPlatformActualTotalAmount(totalPrice);
        orderItem.setSupplierIncomeEarned(totalPrice);
        orderItem.setOriginalPayableUnitPrice(price.getOriginalUnitPrice());
        orderItem.setOriginalPayableTotalAmount(totalPrice);
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        //  platformPayableTotalAmount
        orderItem.setOriginalActualTotalAmount(totalPrice);
        orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
        // 价格/数量  platformActualTotalAmount
        orderItem.setPlatformActualTotalAmount(itemDTO.getChannelTotalSalesAmount());

        orderItem.setPlatformPayableUnitPrice(price.getOriginalUnitPrice());
        orderItem.setPlatformPayableTotalAmount(totalPrice);
        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setPlatformActualUnitPrice(itemDTO.getPrice());
        orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
        orderItem.setChannelSaleUnitPrice(price.getOriginalUnitPrice());
        orderItem.setChannelSaleTotalAmount(totalPrice);


        return orderItem;
    }

    @Override
    public OrderItem setChannelTag(OrderItem orderItem, ErpSaleOrderDTO erpSaleOrderDTO, Orders orders,
                                   ErpSaleOrderItemDTO saleOrderItemDTO) {
        TenantSalesChannel tenantSalesChannel = TenantHelper.ignore(() -> tenantSalesChannelService.getByChannelFlag(erpSaleOrderDTO.getChannelFlag(), ChannelTypeEnum.MultiChannel.getValue()));
        ErpSaleOrderItemDTO dto = erpSaleOrderDTO.getSaleOrderItemsList().get(0);
        String sellerSku = dto.getSellerSku();
        ProductSku productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, sellerSku)));
        orderItem.setChannelType(ChannelTypeEnum.MultiChannel);

        orderItem.setSupplierTenantId(productSku.getTenantId());
        orderItem.setChannelId(tenantSalesChannel.getId());
        orderItem.setChannelOrderNo(erpSaleOrderDTO.getOrderNo());
        return orderItem;
    }

    /**
     * 功能描述：设置订单业务字段 这里的金额逻辑适用于order表的金额计算
     *
     * @param orderItem                订单项目
     * @param orderReceiveFromThirdDTO 从第三dto接收订单
     * @param channelTypeEnum          通道类型枚举
     * @param saleOrderItemDTO         销售订单项目dto
     * <AUTHOR>
     * @date 2024/07/03
     */
    @Override
    public void setOrderBusinessField(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,
                                      ChannelTypeEnum channelTypeEnum, SaleOrderItemDTO saleOrderItemDTO) {
        SaleOrderItemDTO itemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        String erpSku = itemDTO.getErpSku();
        ProductSkuPrice price = null;
        RuleLevelProductPrice memberPrice = null;
        Product product ;
        ProductSkuPrice finalPrice;
        ProductSku sku;
        BigDecimal originalTotalPriceForTikTok = BigDecimal.ZERO;
        BigDecimal originalPayableUnitPriceForTiktok = BigDecimal.ZERO;
        BigDecimal platformPayableUnitPrice = BigDecimal.ZERO;
        TikTokRecipientAddress address = orderReceiveFromThirdDTO.getAddress();

        Long siteId = null;
        siteId = orderItemWrapper(orderItem, address);
        Long finalSiteId = siteId;
        if (ObjectUtil.isNotEmpty(erpSku)){
            if (ChannelTypeEnum.TikTok.name().equals(channelTypeEnum.name())) {

                price = TenantHelper.ignore(() -> iProductSkuPriceService.queryByProductSkuCodeAndSite(erpSku, finalSiteId));
            }
            if (ChannelTypeEnum.Erp.name().equals(channelTypeEnum.name())) {
                price = TenantHelper.ignore(() -> productAboutManger.getProductPriceBySellerSku(erpSku, finalSiteId));
            }
            finalPrice = price;
            sku = TenantHelper.ignore(()->iProductSkuService.getById(finalPrice.getProductSkuId()));
            product = TenantHelper.ignore(()->iProductService.getById(sku.getProductId())) ;

            memberPrice = ruleLevelProductPriceService.getMemberPrice(product.getTenantId(), orderReceiveFromThirdDTO.getTenantId(), price.getProductSkuId(), siteId);
            if(ObjectUtil.isNotEmpty(memberPrice)){
                originalTotalPriceForTikTok = memberPrice.getOriginalUnitPrice();
            }else{
                originalTotalPriceForTikTok = price.getOriginalUnitPrice();
            }
            // 会员定价
            if(ObjectUtil.isNotEmpty(memberPrice)){
                originalPayableUnitPriceForTiktok = memberPrice.getOriginalUnitPrice();
            }else{
                originalPayableUnitPriceForTiktok = price.getOriginalUnitPrice();
            }
            if(ObjectUtil.isNotEmpty(memberPrice)){
                platformPayableUnitPrice = memberPrice.getPlatformUnitPrice();
            }else{
                platformPayableUnitPrice = price.getPlatformUnitPrice();
            }
        }

        orderItem.setShippingOrderState(ShippingOrderStateEnum.Created);
        orderItem.setRestockQuantity(0);

        String status = getOrderStatus(orderReceiveFromThirdDTO, channelTypeEnum);

        orderItem.setOrderState(OrderStateType.getByName(status));
        orderItem.setFulfillmentProgress(LogisticsProgress.UnDispatched);
        orderItem.setDispatchedTime(null);
        orderItem.setFulfillmentTime(null);
        orderItem.setActivityCode(null);

        BigDecimal totalPrice = getTotalPriceByChannelType(orderItem.getChannelType()
                                                                    .name(), BigDecimal.ZERO, itemDTO, price);


        BigDecimal originalTotalPrice = originalTotalPriceForTikTok
            .multiply(BigDecimal.valueOf(itemDTO.getQuantity()));

        // 产品价格-分销商 -拿产品表价格

        orderItem.setSupplierIncomeEarned(totalPrice);


//        orderItem.setProductU
        orderItem.setOriginalPayableUnitPrice(originalPayableUnitPriceForTiktok);
        orderItem.setOriginalPayableTotalAmount(totalPrice);
        // 此处为0是供应商不在分销平台内看到实际的售额
        orderItem.setOriginalPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setOriginalPrepaidTotalAmount(BigDecimal.ZERO);
        // x
        orderItem.setOriginalActualTotalAmount(originalTotalPrice);
        orderItem.setOriginalRefundExecutableAmount(BigDecimal.ZERO);
        // 价格/数量 取的是订单价格
//        BigDecimal platformTotalAmount = new BigDecimal(orderReceiveFromThirdDTO.getTotalAmount());
        // 单价

        BigDecimal platformTotalAmount = getPlatformTotalAmount(orderItem.getChannelType()
                                                                         .name(), BigDecimal.ZERO, itemDTO, price, orderReceiveFromThirdDTO);

        orderItem.setPlatformPayableUnitPrice(platformPayableUnitPrice);

        orderItem.setPlatformPayableTotalAmount(platformTotalAmount);
        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidUnitPrice(BigDecimal.ZERO);
        orderItem.setPlatformPrepaidTotalAmount(BigDecimal.ZERO);

        BigDecimal unitPrice = getUnitPrice(orderItem.getChannelType()
                                                     .name(), BigDecimal.ZERO, itemDTO, price, orderReceiveFromThirdDTO, null);
        orderItem.setPlatformActualUnitPrice(unitPrice);

        orderItem.setPlatformRefundExecutableAmount(BigDecimal.ZERO);
        orderItem.setChannelSaleUnitPrice(unitPrice);
        orderItem.setChannelSaleTotalAmount(platformTotalAmount);
        orderItem.setPlatformActualTotalAmount(platformTotalAmount);

    }

    /**
     * 功能描述：订单商品包装
     *
     * @param orderItem 订购项目
     * @param address   地址
     * @return {@link Long }
     * <AUTHOR>
     * @date 2025/02/07
     */
    private Long orderItemWrapper(OrderItem orderItem, TikTokRecipientAddress address) {
        Long siteId;
        String regionCode;
        SiteCountryCurrency siteByCountryCode;
        if(ObjectUtil.isNotEmpty(address) && StringUtils.isNotEmpty(address.getRegionCode())){
            regionCode = address.getRegionCode();
            siteByCountryCode = iSiteCountryCurrencyService.getSiteByCountryCode(regionCode);
            siteId = siteByCountryCode.getId();
        }else {
            siteByCountryCode = iSiteCountryCurrencyService.getSiteByCountryCode("US");
            siteId = siteByCountryCode.getId();
        }
        orderItem.setCurrencySymbol(siteByCountryCode.getCurrencySymbol());
        orderItem.setCurrency(siteByCountryCode.getCurrencyCode());
        orderItem.setCountryCode(siteByCountryCode.getCountryCode());
        orderItem.setSiteId(siteId);
        return siteId;
    }

    @Override
    public void setChannelTag(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO,
                              ChannelTypeEnum channelTypeEnum, SaleOrderItemDTO saleOrderItemDTO) {
        SaleOrderItemDTO itemDTO = orderReceiveFromThirdDTO.getSaleOrderItemsList().get(0);
        String sellerSku = itemDTO.getErpSku();
        ProductSku productSku;
        if(ObjectUtil.isNotEmpty(sellerSku)){
            if (ChannelTypeEnum.TikTok.name().equals(channelTypeEnum.name())) {
                productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getProductSkuCode, sellerSku)));

            } else {
                productSku = TenantHelper.ignore(() -> productSkuService.getOne(new LambdaQueryWrapper<ProductSku>().eq(ProductSku::getSku, sellerSku)));
            }

            orderItem.setSupplierTenantId(productSku.getTenantId());
        }

        // 拿product的值
        orderItem.setChannelType(orderItem.getChannelType());
//        SysTenant sysTenant = baseMapper.getSysTenantByThirdChannelFlag(orderReceiveFromErpDTO.getThirdChannelFlag());
        TenantSalesChannel tenantSalesChannel = iTenantSalesChannelService.getByChannelFlag(orderReceiveFromThirdDTO.getThirdChannelFlag(), orderItem.getChannelType()
                                                                                                                                                     .name());
        orderItem.setChannelId(tenantSalesChannel.getId());

        orderItem.setChannelOrderNo(orderReceiveFromThirdDTO.getOrderNo());
    }

    @Override
    public void updateOrSetNUll(List<OrderItem> orderItemUpdateList, Integer exceptionCode) {
        // 先把价格都更新进去,然后二次更新的时候用updateWrapper更新null
        if(CollUtil.isNotEmpty(orderItemUpdateList)){
            iOrderItemService.updateBatchByIdNoTenant(orderItemUpdateList);
        }
        List<Long> ids = orderItemUpdateList.stream().map(OrderItem::getId).collect(Collectors.toList());
        if(OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(exceptionCode)
            ||OrderExceptionEnum.measurement_anomaly.getValue().equals(exceptionCode)){
            LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            orderItemLambdaUpdateWrapper
                .set(OrderItem::getSupplierIncomeEarned, null)
                .set(OrderItem::getOriginalPayableUnitPrice, null)
                .set(OrderItem::getOriginalPayableTotalAmount, null)
                .set(OrderItem::getOriginalActualUnitPrice, null)
                .set(OrderItem::getOriginalActualTotalAmount, null)
                .set(OrderItem::getOriginalRefundExecutableAmount, null)
                .set(OrderItem::getPlatformPayableUnitPrice, null)
                .set(OrderItem::getPlatformPayableTotalAmount, null)
                .set(OrderItem::getPlatformActualUnitPrice, null)
                .set(OrderItem::getPlatformActualTotalAmount, null)
                .set(OrderItem::getPlatformRefundExecutableAmount, null)
                .in(OrderItem::getId,ids);
            iOrderItemService.update(orderItemLambdaUpdateWrapper);
        }

    }

    @Override
    public void updateBatchOrSetNUll(List<OrderItem> orderItemNewList, HashMap<String, Integer> codesMap) {
        ArrayList<String> orderNos = new ArrayList<>();
        ArrayList<OrderItem> updateItems = new ArrayList<>();
        ArrayList<Long> ids = new ArrayList<>();
        Boolean isNeedSetNUll = false;
        //codesMap遍历,如果Integer==5,将key的值存入orderNos
        for (Map.Entry<String, Integer> entry : codesMap.entrySet()) {
            // 考虑一点,库存异常实际也是没有尾程派送费的 后续会补充 tag lty todo 自提的库存异常是不用额外操作的 自提可能会有库存
            if (OrderExceptionEnum.final_delivery_fee_exception.getValue().equals(entry.getValue())
                ||OrderExceptionEnum.out_of_stock_exception.getValue().equals(entry.getValue())
                ||OrderExceptionEnum.measurement_anomaly.getValue().equals(entry.getValue())) {
                orderNos.add(entry.getKey());
                isNeedSetNUll = true;
            }
        }

        // orderItemNewList 遍历,如果orderNos包含orderItemNewList的orderNo,将orderItemNewList的元素存入deliveryExceptionItems,否则存入updateItems
        for (OrderItem item : orderItemNewList) {
            if (orderNos.contains(item.getOrderNo())) {
                ids.add(item.getId());
            } else {
                updateItems.add(item);
            }
        }

        // 正常更新
        if(CollUtil.isNotEmpty(orderItemNewList)){
            iOrderItemService.updateBatchByIdNoTenant(orderItemNewList);
        }
        // 对null填充项目的进行二次更新
        if(isNeedSetNUll){
            LambdaUpdateWrapper<OrderItem> orderItemLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            orderItemLambdaUpdateWrapper
                .set(OrderItem::getSupplierIncomeEarned, null)
                .set(OrderItem::getOriginalPayableUnitPrice, null)
                .set(OrderItem::getOriginalPayableTotalAmount, null)
                .set(OrderItem::getOriginalActualUnitPrice, null)
                .set(OrderItem::getOriginalActualTotalAmount, null)
                .set(OrderItem::getOriginalRefundExecutableAmount, null)
                .set(OrderItem::getPlatformPayableUnitPrice, null)
                .set(OrderItem::getPlatformPayableTotalAmount, null)
                .set(OrderItem::getPlatformActualUnitPrice, null)
                .set(OrderItem::getPlatformActualTotalAmount, null)
                .set(OrderItem::getPlatformRefundExecutableAmount, null)
                .in(OrderItem::getId,ids);
            iOrderItemService.update(orderItemLambdaUpdateWrapper);

        }

    }

    @Override
    public void setSiteField(OrderItem orderItem, OrderReceiveFromThirdDTO orderReceiveFromThirdDTO) {
        SiteMsgBo siteBo = orderReceiveFromThirdDTO.getAddress().getSiteBo();

        orderItem.setCurrency(siteBo.getCurrencyCode());
        orderItem.setCurrencySymbol(siteBo.getCurrencySymbol());
        orderItem.setSiteId(siteBo.getSiteId());
        orderItem.setCountryCode(siteBo.getCountryCode());
    }
}
