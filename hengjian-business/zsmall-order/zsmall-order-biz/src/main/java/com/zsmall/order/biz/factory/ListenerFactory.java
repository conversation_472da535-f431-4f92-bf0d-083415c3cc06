package com.zsmall.order.biz.factory;

import com.zsmall.order.biz.listener.OrderItemTrackingListener;
import com.zsmall.order.biz.service.OrderItemTrackingImportRecordService;
import com.zsmall.order.entity.iservice.*;
import com.zsmall.product.biz.service.ProductSkuStockService;
import com.zsmall.product.entity.iservice.IProductSkuService;
import com.zsmall.system.entity.iservice.IDownloadRecordService;
import com.zsmall.system.entity.iservice.ITenantSalesChannelService;
import com.zsmall.warehouse.entity.iservice.IWarehouseService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/3/31 16:05
 */
@Component
public class ListenerFactory {
    @Resource
    private OrderItemTrackingImportRecordService orderItemTrackingImportRecordService;
    @Resource
    private IProductSkuService iProductSkuService;
    @Resource
    private IOrderAddressInfoService iOrderAddressInfoService;
    @Resource
    private IOrdersService iOrdersService;

    @Resource
    private IOrderItemTrackingRecordService itemTrackingRecordService;

    @Resource
    private IOrderItemService iOrderItemService;

    @Resource
    private IOrderLogisticsInfoService iOrderLogisticsInfoService;

    @Resource
    private ProductSkuStockService productSkuStockService;
    @Resource
    private OrderCodeGenerator orderCodeGenerator;
    @Resource
    private IDownloadRecordService iDownloadRecordService;

    @Resource
    private IOrderItemProductSkuService iOrderItemProductSkuService;
    @Resource
    private IWarehouseService iWarehouseService;
    @Resource
    private ITenantSalesChannelService iTenantSalesChannelService;

    public OrderItemTrackingListener createListener(String fileName) {
        OrderItemTrackingListener listener = new OrderItemTrackingListener(
            iTenantSalesChannelService,
            orderItemTrackingImportRecordService,
            iProductSkuService,
            iOrderAddressInfoService,
            iOrdersService,
            iWarehouseService,
            itemTrackingRecordService,
            iOrderItemService,
            iOrderLogisticsInfoService,
            productSkuStockService,
            orderCodeGenerator,
            iDownloadRecordService,
            iOrderItemProductSkuService
        );
        listener.setFileName(fileName);
        return listener;
    }
}
