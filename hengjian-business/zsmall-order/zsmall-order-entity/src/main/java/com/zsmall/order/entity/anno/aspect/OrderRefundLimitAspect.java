package com.zsmall.order.entity.anno.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.zsmall.common.enums.common.CancelOrderApiEnum;
import com.zsmall.common.enums.common.IdempotencyStatus;
import com.zsmall.order.entity.anno.annotaion.OrderRefundLimit;
import com.zsmall.order.entity.domain.bo.SubmitRefundApplyBo;
import com.zsmall.order.entity.iservice.IOrderItemService;
import com.zsmall.order.entity.iservice.IOrdersService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.redisson.api.RLock;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/8/12 11:23
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderRefundLimitAspect {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private IOrdersService iOrdersService;
    @Resource
    private IOrderItemService iOrderItemService;



    @Before("@annotation(orderRefundLimit)")
    public void before(JoinPoint joinPoint, OrderRefundLimit orderRefundLimit) throws NoSuchAlgorithmException {
        CancelOrderApiEnum value = orderRefundLimit.value();
        if(CancelOrderApiEnum.DIS_CANCEL_FLOW.equals(value)){
            SubmitRefundApplyBo refundApplyBo = Arrays.stream(joinPoint.getArgs())
                                                      .filter(arg -> arg instanceof SubmitRefundApplyBo)
                                                      .map(arg -> (SubmitRefundApplyBo) arg)
                                                      .findFirst()
                                                      .orElseThrow(() -> new RuntimeException("退款参数缺失"));

            // 获取幂等键：组合订单号和用户ID确保全局唯一性
            idempotentOperationForRefundOrders(orderRefundLimit, refundApplyBo.getOrderNo(),refundApplyBo.getOrderItemNo());
        }
        if(CancelOrderApiEnum.OPEN_API_CANCEL_ORDER_FLOW.equals(value)){
            // 拿list类型
            Object[] args = joinPoint.getArgs();
            for (Object arg : args) {
                if (arg instanceof List) {
                    // 安全类型转换尝试
                    try {
                        List<?> list = (List<?>) arg;
                        if (!list.isEmpty() && list.get(0) instanceof String) {
                            List<String> list1 = list.stream().map(Object::toString).collect(Collectors.toList());
                            for (String orderExtendId : list1) {
                                idempotentOperationForRefundOrders(orderRefundLimit,orderExtendId ,null);
                            }
                        }
                    } catch (Exception e) {
                        // 类型不匹配，继续尝试
                    }
                }
            }


        }

    }

    /**
     * 功能描述：退款订单幂等
     *
     * @param orderRefundLimit 订单退款限额
     * @param orderNo          订单号
     * @param orderItemNo      订单项目编号
     * <AUTHOR>
     * @date 2025/08/12
     */
    private void idempotentOperationForRefundOrders(OrderRefundLimit orderRefundLimit, String orderNo,String orderItemNo) {
        String idempotencyKey = generateIdempotencyKey(orderNo);
        String lockKey = "idempotency:lock:" + idempotencyKey;
        String statusKey = "idempotency:status:" + idempotencyKey;

        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，等待3秒，锁有效期10秒
            if (!lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                throw new RuntimeException("退款处理中，请勿重复提交");
            }

            // 检查是否已处理过该请求
            IdempotencyStatus status = getStatus(statusKey);
            if (status != null) {
                if (status == IdempotencyStatus.PROCESSING) {
                    throw new RuntimeException("退款处理中，请勿重复提交");
                }
                if (status == IdempotencyStatus.COMPLETED) {
                    throw new RuntimeException("退款申请操作已提交，请勿重复提交");
                }
            }

            // 标记为处理中状态
            setProcessingStatus(statusKey, orderRefundLimit);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("操作被中断");
        } finally {
            // 确保当前线程持有锁时才释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @AfterReturning(pointcut = "@annotation(orderRefundLimit)", returning = "result")
    public void afterReturning(JoinPoint joinPoint, OrderRefundLimit orderRefundLimit, Object result) {
        processCompletion(joinPoint, orderRefundLimit, result, true);
    }

    @AfterThrowing(pointcut = "@annotation(orderRefundLimit)", throwing = "ex")
    public void afterThrowing(JoinPoint joinPoint, OrderRefundLimit orderRefundLimit, Exception ex) {
        processCompletion(joinPoint, orderRefundLimit, null, false);
    }
    private SubmitRefundApplyBo getRefundApplyBo(JoinPoint joinPoint) {
        // 获取方法所有参数
        Object[] args = joinPoint.getArgs();

        // 1. 优先查找类型匹配的参数
        for (Object arg : args) {
            if (arg instanceof SubmitRefundApplyBo) {
                return (SubmitRefundApplyBo) arg;
            }
        }
        return null;
    }
    private void processCompletion(JoinPoint joinPoint, OrderRefundLimit orderRefundLimit, Object result, boolean success) {

        CancelOrderApiEnum value = orderRefundLimit.value();
        if(CancelOrderApiEnum.DIS_CANCEL_FLOW.equals(value)){
            SubmitRefundApplyBo refundApplyBo = getRefundApplyBo(joinPoint);
            if(ObjectUtil.isEmpty(refundApplyBo)){
                throw new RuntimeException("数据异常");
            }
            String orderNo = refundApplyBo.getOrderNo();
            lockMethod(result, success, orderNo);
        }
        if(CancelOrderApiEnum.OPEN_API_CANCEL_ORDER_FLOW.equals(value)){
            List<String> refundList = getRefundList(joinPoint);
            if(CollUtil.isEmpty(refundList)){
                throw new RuntimeException("数据异常");
            }
            for (String orderNo : refundList) {
                lockMethod(result, success, orderNo);
            }

        }

    }

    private List<String> getRefundList(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        for (Object arg : args) {
            if (arg instanceof List) {
                // 安全类型转换尝试
                try {
                    List<?> list = (List<?>) arg;
                    if (!list.isEmpty() && list.get(0) instanceof String) {
                        List<String> list1 = list.stream().map(Object::toString).collect(Collectors.toList());
                        return list1;
                    }
                } catch (Exception e) {
                    // 类型不匹配，继续尝试
                }
            }
        }
        return null;
    }

    private void lockMethod(Object result, boolean success, String orderNo) {
        String idempotencyKey = generateIdempotencyKey(orderNo);
        String lockKey = "idempotency:lock:" + idempotencyKey;
        String statusKey = "idempotency:status:" + idempotencyKey;

        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 获取锁更新状态
            if (lock.tryLock(3, 10, TimeUnit.SECONDS)) {
                // 业务执行成功：设置为已完成状态并存储结果
                if (success) {
                    setCompletedStatus(statusKey, result);
                }
                // 业务执行失败：清除状态，允许重试
                else {
                    clearStatus(statusKey);
                }
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    // 生成全局唯一的幂等键（组合用户+订单）
    private String generateIdempotencyKey(String orderNo) {
        return String.format("refund:%s", orderNo);
    }

    // 状态管理方法
    private IdempotencyStatus getStatus(String key) {
        RMapCache<String, String> map = redissonClient.getMapCache(key);
        log.info("redis 退款key,{}", JSON.toJSONString(map));
        if (map != null && map.containsKey("status")) {
            String statusValue = map.get("status");
            if (statusValue != null) {
                try {
                    return IdempotencyStatus.valueOf(statusValue);
                } catch (IllegalArgumentException e) {
                    log.warn("Invalid status value in Redis for key {}: {}", key, statusValue);
                }
            }
        }
        return null;
    }

    private void setProcessingStatus(String key, OrderRefundLimit orderRefundLimit) {
        RMapCache<String, IdempotencyStatus> map = redissonClient.getMapCache(key);
        map.put("status", IdempotencyStatus.PROCESSING, orderRefundLimit.interval(), orderRefundLimit.timeUnit());
    }

    private void setCompletedStatus(String key, Object result) {
        RMapCache<String, Object> map = redissonClient.getMapCache(key);
        // 存储1小时结果，后续相同请求可直接返回
        map.put("status", IdempotencyStatus.COMPLETED, 2, TimeUnit.MINUTES);
        map.put("result", result, 2, TimeUnit.MINUTES);
    }

    private void clearStatus(String key) {
        RMapCache<String, ?> map = redissonClient.getMapCache(key);
        map.delete();
    }
}
