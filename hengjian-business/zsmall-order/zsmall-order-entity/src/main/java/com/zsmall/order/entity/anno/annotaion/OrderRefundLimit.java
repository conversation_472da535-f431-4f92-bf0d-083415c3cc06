package com.zsmall.order.entity.anno.annotaion;

import com.zsmall.common.enums.common.CancelOrderApiEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/8/12 11:22
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface OrderRefundLimit {
    /**
     * 功能描述：时间单位
     *
     * @return {@link TimeUnit }
     * <AUTHOR>
     * @date 2024/03/08
     */
    TimeUnit timeUnit() default TimeUnit.SECONDS;

    /**
     * 功能描述：间隔
     *
     * @return int
     * <AUTHOR>
     * @date 2024/03/08
     */
    int interval() default 60;

    CancelOrderApiEnum value() default CancelOrderApiEnum.DIS_CANCEL_FLOW;
}
