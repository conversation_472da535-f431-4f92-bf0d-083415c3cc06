package com.zsmall.activity.biz.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.common.redis.utils.QueueUtils;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 基于Redis的活动过期服务
 * 解决RabbitMQ TTL队列消息阻塞问题
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityExpireRedisService {

    private static final String ACTIVITY_EXPIRE_QUEUE = "activity_expire_queue";

    /**
     * 发送活动过期延时消息（Redis方案）
     *
     * @param activeCode 活动编码
     * @param type       活动类型 1-供应商活动 2-分销商活动
     * @param endTime    活动结束时间
     */
    public void sendActivityExpireMessage(String activeCode, Integer type, Date endTime) {
        try {
            // 计算延时时间
            long currentTime = System.currentTimeMillis();
            long endTimeMillis = endTime.getTime();
            long delayTime = endTimeMillis - currentTime;

            if (delayTime <= 0) {
                log.warn("活动已过期，无需发送延时消息: 活动编码={}, 结束时间={}", activeCode, DateUtil.formatDateTime(endTime));
                return;
            }

            // 构造消息体
            ActivityExpireMq mq = new ActivityExpireMq();
            mq.setActiveCode(activeCode);
            mq.setType(type);
            String messageBody = JSONUtil.toJsonStr(mq);

            // 发送到Redis延时队列
            QueueUtils.addDelayedQueueObject(ACTIVITY_EXPIRE_QUEUE, messageBody, delayTime, TimeUnit.MILLISECONDS);
            
            log.info("发送活动过期延时消息成功(Redis): 活动编码={}, 活动类型={}, 到期时间={}, 延时={}ms", 
                    activeCode, type, DateUtil.formatDateTime(endTime), delayTime);
        } catch (Exception e) {
            log.error("发送活动过期消息失败(Redis): 活动编码={}", activeCode, e);
        }
    }

    /**
     * 初始化Redis延时队列消费者
     * 在应用启动时调用
     */
    public void initActivityExpireConsumer() {
        log.info("初始化活动过期Redis延时队列消费者");
        QueueUtils.subscribeBlockingQueue(ACTIVITY_EXPIRE_QUEUE, (String messageBody) -> {
            try {
                log.info("收到活动过期消息(Redis): {}", messageBody);
                
                ActivityExpireMq mq = JSONUtil.toBean(messageBody, ActivityExpireMq.class);
                if (mq == null || mq.getActiveCode() == null || mq.getType() == null) {
                    log.error("活动过期消息格式错误: {}", messageBody);
                    return;
                }

                // 这里可以直接调用业务处理逻辑，或者转发到RabbitMQ处理队列
                handleActivityExpire(mq);
                
            } catch (Exception e) {
                log.error("处理活动过期消息失败(Redis): {}", messageBody, e);
            }
        });
    }

    /**
     * 处理活动过期逻辑
     */
    private void handleActivityExpire(ActivityExpireMq mq) {
        // 这里可以选择：
        // 1. 直接调用业务逻辑
        // 2. 转发到RabbitMQ处理队列保持原有架构
        
        log.info("处理活动过期: 活动编码={}, 类型={}", mq.getActiveCode(), mq.getType());
        
        // 方案1：直接处理（推荐，减少中间环节）
        // if (mq.getType() == 1) {
        //     productActiveService.handleSupplierActivityExpire(mq.getActiveCode());
        // } else {
        //     productActiveService.handleDistributorActivityExpire(mq.getActiveCode());
        // }
        
        // 方案2：转发到RabbitMQ处理队列（保持原有架构）
        // rabbitTemplate.convertAndSend(
        //     RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE,
        //     JSONUtil.toJsonStr(mq)
        // );
    }
}
