package com.zsmall.activity.biz.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 基于多队列的活动过期服务
 * 按延时时间分队列，解决消息阻塞问题
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityExpireMultiQueueService {

    private final RabbitTemplate rabbitTemplate;

    // 定义不同延时时间的队列
    private static final String QUEUE_1_HOUR = "activity.expire.1h.queue";
    private static final String QUEUE_6_HOUR = "activity.expire.6h.queue";
    private static final String QUEUE_1_DAY = "activity.expire.1d.queue";
    private static final String QUEUE_3_DAY = "activity.expire.3d.queue";
    private static final String QUEUE_1_WEEK = "activity.expire.1w.queue";
    private static final String QUEUE_1_MONTH = "activity.expire.1m.queue";

    /**
     * 发送活动过期延时消息（多队列方案）
     *
     * @param activeCode 活动编码
     * @param type       活动类型 1-供应商活动 2-分销商活动
     * @param endTime    活动结束时间
     */
    public void sendActivityExpireMessage(String activeCode, Integer type, Date endTime) {
        try {
            // 计算延时时间
            long currentTime = System.currentTimeMillis();
            long endTimeMillis = endTime.getTime();
            long delayTime = endTimeMillis - currentTime;

            if (delayTime <= 0) {
                log.warn("活动已过期，无需发送延时消息: 活动编码={}, 结束时间={}", activeCode, DateUtil.formatDateTime(endTime));
                return;
            }

            // 构造消息体
            ActivityExpireMq mq = new ActivityExpireMq();
            mq.setActiveCode(activeCode);
            mq.setType(type);
            String messageBody = JSONUtil.toJsonStr(mq);

            // 根据延时时间选择合适的队列
            String queueName = selectQueueByDelayTime(delayTime);
            String exchangeName = getExchangeByQueue(queueName);

            // 发送消息到对应队列
            rabbitTemplate.convertAndSend(
                exchangeName,
                RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY,
                messageBody,
                message -> {
                    // 设置消息TTL
                    message.getMessageProperties().setExpiration(String.valueOf(delayTime));
                    return message;
                }
            );

            log.info("发送活动过期延时消息成功(多队列): 活动编码={}, 活动类型={}, 队列={}, 到期时间={}, 延时={}ms", 
                    activeCode, type, queueName, DateUtil.formatDateTime(endTime), delayTime);
        } catch (Exception e) {
            log.error("发送活动过期消息失败(多队列): 活动编码={}", activeCode, e);
        }
    }

    /**
     * 根据延时时间选择合适的队列
     */
    private String selectQueueByDelayTime(long delayTime) {
        long delaySeconds = delayTime / 1000;
        
        if (delaySeconds <= TimeUnit.HOURS.toSeconds(1)) {
            return QUEUE_1_HOUR;
        } else if (delaySeconds <= TimeUnit.HOURS.toSeconds(6)) {
            return QUEUE_6_HOUR;
        } else if (delaySeconds <= TimeUnit.DAYS.toSeconds(1)) {
            return QUEUE_1_DAY;
        } else if (delaySeconds <= TimeUnit.DAYS.toSeconds(3)) {
            return QUEUE_3_DAY;
        } else if (delaySeconds <= TimeUnit.DAYS.toSeconds(7)) {
            return QUEUE_1_WEEK;
        } else {
            return QUEUE_1_MONTH;
        }
    }

    /**
     * 根据队列名获取对应的交换机名
     */
    private String getExchangeByQueue(String queueName) {
        // 可以为不同队列配置不同交换机，或使用同一个交换机
        return RabbitMqConstant.ACTIVITY_EXPIRE_EXCHANGE;
    }
}

/**
 * 多队列配置类（需要添加到RabbitMqConfig中）
 */
/*
@Configuration
public class ActivityExpireMultiQueueConfig {

    // 1小时队列配置
    @Bean
    public Queue activityExpire1HourTtlQueue() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-dead-letter-exchange", RabbitMqConstant.ACTIVITY_EXPIRE_DLX_EXCHANGE);
        arguments.put("x-dead-letter-routing-key", RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY);
        return new Queue("activity.expire.1h.queue", true, false, false, arguments);
    }

    @Bean
    public Binding activityExpire1HourBinding() {
        return BindingBuilder.bind(activityExpire1HourTtlQueue())
                .to(distributorActivityExpireExchange())
                .with(RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY);
    }

    // 6小时队列配置
    @Bean
    public Queue activityExpire6HourTtlQueue() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-dead-letter-exchange", RabbitMqConstant.ACTIVITY_EXPIRE_DLX_EXCHANGE);
        arguments.put("x-dead-letter-routing-key", RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY);
        return new Queue("activity.expire.6h.queue", true, false, false, arguments);
    }

    @Bean
    public Binding activityExpire6HourBinding() {
        return BindingBuilder.bind(activityExpire6HourTtlQueue())
                .to(distributorActivityExpireExchange())
                .with(RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY);
    }

    // ... 其他队列配置类似
}
*/
