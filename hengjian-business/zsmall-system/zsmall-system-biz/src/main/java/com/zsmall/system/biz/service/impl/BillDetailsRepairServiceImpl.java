package com.zsmall.system.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.mapper.SysUserMapper;
import com.zsmall.system.biz.service.BillHeadSupper;
import com.zsmall.system.biz.service.IBillDetailsRepairService;
import com.zsmall.system.entity.domain.*;
import com.zsmall.system.entity.domain.dto.BillConfirmedSendErpDTO;
import com.zsmall.system.entity.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 账单明细修补表服务实现
 *
 * <AUTHOR> Li
 * @date 2024-08-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BillDetailsRepairServiceImpl implements IBillDetailsRepairService {

    private final BillDetailsRepairMapper billDetailsRepairMapper;
    private final BillDetailsMapper billDetailsMapper;
    private final BillHeadMapper billHeadMapper;
    private final BillHeadSupper billHeadSupper;
    private final RabbitTemplate rabbitTemplate;
    private final TenantSiteMapper tenantSiteMapper;
    private final SysUserMapper sysUserMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void repairBillData(String billNo, String orderNo) {
        log.info("开始修补账单数据，参数：billNo={}, orderNo={}", billNo, orderNo);

        try {
            // 1. 参数校验
            if (StrUtil.isBlank(billNo)) {
                throw new RuntimeException("账单编号不能为空");
            }
            if (StrUtil.isBlank(orderNo)) {
                throw new RuntimeException("订单编号不能为空");
            }

            LambdaQueryWrapper<BillHead> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(BillHead::getBillNo, billNo)
                        .eq(BillHead::getDelFlag, 0);
            BillHead billHead = TenantHelper.ignore(() -> billHeadMapper.selectOne(queryWrapper));
            if (billHead == null) {
                throw new RuntimeException("未找到指定的账单信息：" + billNo);
            }

            // 2. 解析参数
            List<String> orderNos = Arrays.asList(orderNo.split(","));
            log.info("需要修补的账单编号：{}", billNo);
            log.info("需要修补的订单号：{}", orderNos);
            // 3. 清理同一订单的旧未推送修补记录
            cleanupOldUnsentRepairRecords(orderNos, billNo);

            // 4. 备份原账单数据到修补表
            backupBillDetailsToRepairTable(orderNos, billNo);

            // 6. 删除当前账单相关数据
            deleteCurrentMonthBillData(billNo);

            String tenantId = billHead.getTenantId();
            Date billStartTime = billHead.getBillStartTime();
            Date billEndTime = billHead.getBillEndTime();
            // 格式化为字符串，用于后续的账单生成
            String startTime = DateUtil.format(billStartTime, "yyyy-MM-dd HH:mm:ss");
            String endTime = DateUtil.format(billEndTime, "yyyy-MM-dd HH:mm:ss");

            DateTime currentMonthStart = DateUtil.beginOfMonth(billStartTime);
            DateTime currentMonthEnd = DateUtil.endOfMonth(billEndTime);

            String startMonthDay = DateUtil.format(currentMonthStart, "MMdd");
            String endMonthDay = DateUtil.format(currentMonthEnd, "MMdd");
            String countryCode = billHead.getCountryCode();

            // 7. 重新生成当月的账单数据
            billHeadSupper.generatedDistributorBillByTenant(tenantId, false, startMonthDay, endMonthDay, startTime, endTime,countryCode);

            // 8. 生成当月的账单汇总数据
            billHeadSupper.generatedTransactionReceipt(tenantId, false, startMonthDay, endMonthDay, startTime, endTime,countryCode);
            //更新差值
            calculateBillDifferencesInternal( orderNos,billNo);
            log.info("账单数据修补完成，租户ID：{}", tenantId);
        } catch (Exception e) {
            log.error("账单数据修补失败，账单编号：{}，错误信息：{}", billNo, e.getMessage(), e);
            throw new RuntimeException("账单数据修补失败：" + e.getMessage(), e);
        }
    }

    /**
     * 计算账单修补前后的差值（内部方法）
     */
    private void calculateBillDifferencesInternal(List<String> orderNos, String billNo) {
        log.info("开始计算账单差值，订单数量：{}", orderNos.size());

        // 查询修补表中的最新数据（修补前）
        LambdaQueryWrapper<BillDetailsRepair> repairQuery = new LambdaQueryWrapper<>();
        repairQuery.eq(BillDetailsRepair::getBillNo, billNo)
                   .in(BillDetailsRepair::getOrderNo, orderNos)
                   .eq(BillDetailsRepair::getDelFlag, 0);
        List<BillDetailsRepair> repairData = TenantHelper.ignore(() -> billDetailsRepairMapper.selectList(repairQuery));

        // 查询当前账单表中的数据（修补后）
        LambdaQueryWrapper<BillDetails> currentQuery = new LambdaQueryWrapper<>();
        currentQuery.eq(BillDetails::getBillNo, billNo)
                    .in(BillDetails::getOrderNo, orderNos)
                    .eq(BillDetails::getDelFlag, 0);
        List<BillDetails> currentData = TenantHelper.ignore(() -> billDetailsMapper.selectList(currentQuery));

        // 按订单号+订单状态分组（发货单和退款单分别处理）
        Map<String, BillDetailsRepair> repairMap = repairData.stream()
                                                             .collect(Collectors.toMap(
                                                                 repair -> generateCompositeKey(repair.getOrderNo(), repair.getOrderStatus()),
                                                                 Function.identity(),
                                                                 (v1, v2) -> v1));

        Map<String, BillDetails> currentMap = currentData.stream()
                                                         .collect(Collectors.toMap(
                                                             current -> generateCompositeKey(current.getOrderNo(), current.getOrderStatus()),
                                                             Function.identity(),
                                                             (v1, v2) -> v1));

        // 合并数据 - 按复合键匹配，确保发货单与发货单计算差值，退款单与退款单计算差值
        repairMap.forEach((compositeKey, repair) -> {
            BillDetails current = currentMap.get(compositeKey);
            if (current != null) {
                String orderStatusDesc = repair.getOrderStatus() != null ?
                    (repair.getOrderStatus() == 1 ? "发货单" : "退款单") : "未知状态";
                log.debug("计算差值 - 订单号：{}，订单状态：{}", repair.getOrderNo(), orderStatusDesc);

                repair.setProductSkuPrice(current.getProductSkuPrice().subtract(repair.getProductSkuPrice()));
                repair.setOrderTotalAmount(current.getOrderTotalAmount().subtract(repair.getOrderTotalAmount()));
                repair.setOrderRefundTotalAmount(current.getOrderRefundTotalAmount().subtract(repair.getOrderRefundTotalAmount()));
                repair.setOperationFee(current.getOperationFee().subtract(repair.getOperationFee()));
                repair.setFinalDeliveryFee(current.getFinalDeliveryFee().subtract(repair.getFinalDeliveryFee()));
                repair.setUnitPrice(current.getUnitPrice().subtract(repair.getUnitPrice()));

                //  repair.setProductQuantity(current.getProductQuantity() - repair.getProductQuantity());
                TenantHelper.ignore(()->billDetailsRepairMapper.updateById(repair)) ;
            } else {
                log.warn("未找到匹配的当前数据 - 复合键：{}", compositeKey);
            }
        });

        log.info("账单差值计算完成，处理记录数：{}", repairMap.size());
    }

    /**
     * 生成复合键：订单号_订单状态
     * 用于区分同一订单的发货单和退款单
     */
    private String generateCompositeKey(String orderNo, Integer orderStatus) {
        return orderNo + "_" + (orderStatus != null ? orderStatus : 0);
    }
    /**
     * 备份原账单数据到修补表
     */
    private void backupBillDetailsToRepairTable(List<String> orderNos, String billNo) {

        log.info("开始备份账单数据到修补表，租户ID：{}, 订单数量：{}", billNo, orderNos.size());

        // 查询需要备份的账单明细数据
        LambdaQueryWrapper<BillDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(BillDetails::getBillNo, billNo)
                    .in(BillDetails::getOrderNo, orderNos)
                   .eq(BillDetails::getDelFlag, 0);

        List<BillDetails> billDetailsList = TenantHelper.ignore(() -> billDetailsMapper.selectList(queryWrapper));

        if (CollectionUtil.isEmpty(billDetailsList)) {
            log.warn("未找到需要备份的账单数据， 账单编号：{}", billNo);
            return;
        }

        // 转换为修补表数据
        List<BillDetailsRepair> repairList = new ArrayList<>();
        for (BillDetails billDetails : billDetailsList) {
            BillDetailsRepair repair = new BillDetailsRepair();
            // 复制属性时忽略ERP推送相关字段，因为这两个字段的维度不同
            BeanUtil.copyProperties(billDetails, repair, "isSendErp", "sendErpTime");
            repair.setId(IdUtil.getSnowflakeNextId());
            repair.setIsSendErp(0);
            repair.setSendErpTime(null);
            repairList.add(repair);
        }

        // 批量插入修补表
        TenantHelper.ignore(() -> {
            billDetailsRepairMapper.batchInsertBillDetailsRepair(repairList);
            return null;
        });

        log.info("成功备份{}条账单数据到修补表", repairList.size());
    }

    /**
     * 清理同一账单下同一订单的旧未推送修补记录
     * 在新修补操作前，删除相同账单编号下相同订单的所有未推送修补记录，避免重复推送
     * 注意：不同账单编号下的相同订单号应该独立处理
     */
    private void cleanupOldUnsentRepairRecords(List<String> orderNos, String billNo) {
        TenantHelper.ignore(()->billDetailsRepairMapper.delete(new LambdaQueryWrapper<BillDetailsRepair>()
            .eq(BillDetailsRepair::getBillNo, billNo)
            .in(BillDetailsRepair::getOrderNo, orderNos)
            .eq(BillDetailsRepair::getIsSendErp, 0)));
        log.info("旧修补记录清理完成");
    }
    /**

    /**
     * 删除指定账单的数据
     */
    private void deleteCurrentMonthBillData(String billNo) {
        log.info("开始删除指定账单数据，账单编号：{}", billNo);
        int deletedDetails = TenantHelper.ignore(() -> billDetailsMapper.physicalDeleteBySql("DELETE FROM bill_details WHERE bill_no = '" + billNo + "' AND del_flag = 0"));
        log.info("删除账单明细数据{}条", deletedDetails);
        // 删除账单头数据
        int deletedHeads = TenantHelper.ignore(() -> billDetailsMapper.physicalDeleteBySql("DELETE FROM bill_head WHERE bill_no = '" + billNo + "' AND del_flag = 0"));
        log.info("删除账单头数据{}条", deletedHeads);
        // 删除账单汇总数据
        int deletedReceipts = TenantHelper.ignore(() -> billDetailsMapper.physicalDeleteBySql("DELETE FROM bill_transaction_receipt WHERE bill_no = '" + billNo + "' AND del_flag = 0"));
        log.info("删除账单汇总数据{}条", deletedReceipts);
    }


    /**
     * 推送修补数据到ERP
     * @param repairDataList 修补数据列表
     */
    public void sendRepairDataToErpWithNewQueue(List<BillDetailsRepair> repairDataList) {
        log.info("[账单修补数据推送ERP], 开始处理修补数据推送，数据量：{}", repairDataList.size());

        if (CollectionUtil.isEmpty(repairDataList)) {
            log.warn("[账单修补数据推送ERP], 修补数据列表为空");
            return;
        }

        try {
            // 获取租户站点信息映射 (tenantId + countryCode -> TenantSite)
            List<TenantSite> tenantSites = TenantHelper.ignore(() -> tenantSiteMapper.selectList(new LambdaQueryWrapper<>()));
            Map<String, TenantSite> tenantSiteMap = tenantSites.stream()
                .collect(Collectors.toMap(
                    site -> site.getTenantId() + site.getCountryCode(),
                    Function.identity(),
                    (existingValue, newValue) -> newValue
                ));

            // 获取用户信息映射 (tenantId -> SysUserVo)
            List<SysUserVo> sysUserVos = TenantHelper.ignore(() -> sysUserMapper.selectVoList());
            Map<String, SysUserVo> userMap = sysUserVos.stream()
                .collect(Collectors.toMap(
                    SysUserVo::getTenantId,
                    user -> user,
                    (existing, replacement) -> existing
                ));
            //获取所有的billNo
            List<String> billNos = repairDataList.stream().map(BillDetailsRepair::getBillNo).distinct().collect(Collectors.toList());
            LambdaQueryWrapper<BillHead> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(BillHead::getBillNo,billNos)
                        .eq(BillHead::getDelFlag,0);
            List<BillHead> billHeads = TenantHelper.ignore(() -> billHeadMapper.selectList(queryWrapper));
            Map<String, BillHead> billHeadMap = billHeads.stream()
                .collect(Collectors.toMap(BillHead::getBillNo, Function.identity(), (v1, v2) -> v1));

            for (BillDetailsRepair repair : repairDataList) {
                AtomicInteger counter = new AtomicInteger(0);
                BillHead billHead = billHeadMap.get(repair.getBillNo());

                BillConfirmedSendErpDTO b=new BillConfirmedSendErpDTO();
                b.setIsLastData(counter.getAndIncrement() == repairDataList.size()  - 1);
                b.setBillConfirmedTime(billHead.getBillEndTime());
                TenantSite tenantSite = tenantSiteMap.get(billHead.getTenantId() + repair.getCountryCode());
                if (ObjectUtil.isNotNull(tenantSite)){
                    b.setThirdChannelFlag(tenantSite.getChannelFlag());
                }else {
                    b.setThirdChannelFlag(tenantSite.getChannelFlag());
                }
                b.setBillNo(repair.getBillNo());
                b.setCurrencyCode(repair.getCurrencyCode());
                b.setTotal(billHead.getTotal());
                BillConfirmedSendErpDTO.BillDetailsConfirmedSendErpDTO bd = BeanUtil.copyProperties(repair, BillConfirmedSendErpDTO.BillDetailsConfirmedSendErpDTO.class);
                if (bd.getOrderStatus()!=2){
                    bd.setOrderRefundTotalAmount(null);
                }
                bd.setOrderNo(repair.getOrderExtendId());
                bd.setSupplierTenantId(repair.getSupperTenantId());
                bd.setBillConfirmedTime(billHead.getBillEndTime());
                SysUserVo sysUserVo = userMap.get(bd.getSupplierTenantId());
                if (ObjectUtil.isNotNull(sysUserVo)){
                    bd.setNickName(sysUserVo.getNickName());
                }else {
                    bd.setNickName("");
                }
                b.setBillDetailsConfirmedSendErpDTOS(bd);
                log.info("[账单确认推送ERP],发送消息,内容:{}", JSON.toJSONString(b));
                rabbitTemplate.convertAndSend(RabbitMqConstant.MULTICHANNEL_SEND_EXCHANGE, RabbitMqConstant.BILL_REPAIR_SEND_ERP_REQUEST_QUEUE, JSON.toJSONString(b));
                //更新明细发送时间
                TenantHelper.ignore(()  -> {
                    UpdateWrapper<BillDetailsRepair> wrapper = new UpdateWrapper<>();
                    wrapper.set("is_send_erp",  1)
                           .set("send_erp_time",new Date())
                           .eq("id", repair.getId());
                    billDetailsRepairMapper.update(null,  wrapper);
                });
            }
            log.info("[账单修补数据推送ERP], 全部修补数据推送完成，总计处理：{} 条记录", repairDataList.size());
        } catch (Exception e) {
            log.error("[账单修补数据推送ERP] 处理失败，错误信息：{}", e.getMessage(), e);
            throw new RuntimeException("账单修补数据推送ERP失败：" + e.getMessage(), e);
        }
    }

    /**
     * 重新推送差值数据到ERP(手动推送)
     * @param billNo 账单编号
     */
    @Override
    public void resendDifferenceToErp(String billNo) {
        log.info("重新推送差值数据到ERP，账单编号：{}", billNo);

        try {
            LambdaQueryWrapper<BillDetailsRepair> wq = new LambdaQueryWrapper<>();
            wq.eq(BillDetailsRepair::getBillNo, billNo)
              .eq(BillDetailsRepair::getIsSendErp, 0)
              .eq(BillDetailsRepair::getDelFlag, 0);
            List<BillDetailsRepair> billDetailsRepairs = TenantHelper.ignore(() -> billDetailsRepairMapper.selectList(wq));

            // 使用新队列推送到erp
            if (CollectionUtil.isNotEmpty(billDetailsRepairs)) {
                sendRepairDataToErpWithNewQueue(billDetailsRepairs);
                log.info("重新推送差值数据到ERP完成，账单编号：{}, 推送记录数：{}", billNo, billDetailsRepairs.size());
            } else {
                log.warn("未找到需要重新推送的修补数据，账单编号：{}", billNo);
            }

        } catch (Exception e) {
            log.error("重新推送差值数据到ERP失败，账单编号：{}，错误信息：{}", billNo, e.getMessage(), e);
            throw new RuntimeException("重新推送差值数据到ERP失败：" + e.getMessage(), e);
        }
    }
    /**
     * 推送修补表中未推送的全量数据到ERP（定时任务使用）
     */
    @Override
    public void pushUnsentRepairDataToErp() {
        log.info("开始推送修补表中未推送的全量数据到ERP");

        try {
            // 查询当前时间上个月的未推送的数据
            DateTime dateTime = DateUtil.offsetMonth(DateUtil.date(), -1);
            //转换成字符串
            String startTime = DateUtil.format(DateUtil.beginOfMonth(dateTime), "yyyyMMdd");
            LambdaQueryWrapper<BillDetailsRepair> o = new LambdaQueryWrapper<>();
            o.eq(BillDetailsRepair::getIsSendErp, 0)
             .eq(BillDetailsRepair::getDelFlag, 0)
             .like(BillDetailsRepair::getBillNo, startTime);
            List<BillDetailsRepair> unsentData = TenantHelper.ignore(() -> billDetailsRepairMapper.selectList(o));
            sendRepairDataToErpWithNewQueue(unsentData);
            log.info("修补表未推送数据推送任务执行完成");
        } catch (Exception e) {
            log.error("推送修补表未推送数据失败：{}", e.getMessage(), e);
            throw new RuntimeException("推送修补表未推送数据失败：" + e.getMessage(), e);
        }
    }
}
