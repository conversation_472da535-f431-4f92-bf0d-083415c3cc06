package com.zsmall.common.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/8/12 15:34
 */
@Getter
@AllArgsConstructor
public enum CancelOrderApiEnum implements IEnum<String> {

    OPEN_API_CANCEL_ORDER_FLOW("cancelOrderFlow"),
    DIS_CANCEL_FLOW("disCancelFlow"),;

    private String apiName;
    @Override
    public String getValue() {
        return this.name();
    }

}
