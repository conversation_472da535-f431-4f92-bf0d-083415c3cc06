package com.zsmall.common.enums.common;

/**
 * lty notes
 *
 * <AUTHOR> Theo
 * @create 2025/8/12 14:44
 */
public enum IdempotencyStatus {
    PROCESSING("processing"),
    COMPLETED("completed");

    private final String value;

    IdempotencyStatus(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return this.value;
    }

    public static IdempotencyStatus fromValue(String value) {
        for (IdempotencyStatus status : values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知状态: " + value);
    }
}
